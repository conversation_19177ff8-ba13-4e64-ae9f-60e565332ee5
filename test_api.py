#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智谱AI API接口测试工具
用于测试GLM-4.5-Flash模型的API连通性和功能
"""

import json
import requests
import time
import sys
from typing import Dict, Any

class APITester:
    def __init__(self):
        self.api_key = "e601f05d308a42be8d5f3e40a98ea324.aPTWcglZd4DljsUY"
        self.base_url = "https://open.bigmodel.cn/api/paas/v4/chat/completions"
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        self.test_results = []
    
    def print_header(self, title: str):
        """打印测试标题"""
        print("\n" + "="*60)
        print(f" {title}")
        print("="*60)
    
    def print_test(self, test_name: str, status: str, details: str = ""):
        """打印测试结果"""
        status_symbol = "✓" if status == "PASS" else "✗" if status == "FAIL" else "⚠"
        print(f"{status_symbol} {test_name}: {status}")
        if details:
            print(f"   详情: {details}")
        
        self.test_results.append({
            "test": test_name,
            "status": status,
            "details": details
        })
    
    def test_network_connectivity(self) -> bool:
        """测试网络连通性"""
        self.print_header("网络连通性测试")
        
        try:
            # 测试基础网络连接
            response = requests.get("https://www.baidu.com", timeout=10)
            if response.status_code == 200:
                self.print_test("基础网络连接", "PASS", "可以访问外部网站")
            else:
                self.print_test("基础网络连接", "FAIL", f"HTTP状态码: {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            self.print_test("基础网络连接", "FAIL", f"网络错误: {str(e)}")
            return False
        
        try:
            # 测试智谱AI域名解析
            response = requests.get("https://open.bigmodel.cn", timeout=10)
            self.print_test("智谱AI域名解析", "PASS", "可以访问智谱AI服务器")
            return True
        except requests.exceptions.RequestException as e:
            self.print_test("智谱AI域名解析", "FAIL", f"无法访问智谱AI: {str(e)}")
            return False
    
    def test_api_authentication(self) -> bool:
        """测试API认证"""
        self.print_header("API认证测试")
        
        # 测试无效请求（用于验证服务器响应）
        try:
            invalid_payload = {
                "model": "invalid-model",
                "messages": [{"role": "user", "content": "test"}]
            }
            
            response = requests.post(
                self.base_url,
                headers=self.headers,
                json=invalid_payload,
                timeout=30
            )
            
            if response.status_code == 401:
                self.print_test("API认证", "FAIL", "API密钥无效或已过期")
                return False
            elif response.status_code == 403:
                self.print_test("API认证", "FAIL", "API密钥权限不足")
                return False
            elif response.status_code in [400, 422]:
                self.print_test("API认证", "PASS", "API密钥有效，服务器正常响应")
                return True
            else:
                self.print_test("API认证", "WARNING", f"意外的响应状态码: {response.status_code}")
                return True
                
        except requests.exceptions.RequestException as e:
            self.print_test("API认证", "FAIL", f"请求失败: {str(e)}")
            return False
    
    def test_api_functionality(self) -> bool:
        """测试API功能"""
        self.print_header("API功能测试")
        
        test_payload = {
            "model": "GLM-4-Flash",
            "messages": [
                {
                    "role": "user",
                    "content": "请回复'API测试成功'这5个字，不要添加任何其他内容。"
                }
            ],
            "temperature": 0.1,
            "max_tokens": 50,
            "stream": False
        }
        
        try:
            start_time = time.time()
            response = requests.post(
                self.base_url,
                headers=self.headers,
                json=test_payload,
                timeout=60
            )
            end_time = time.time()
            response_time = round(end_time - start_time, 2)
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    if 'choices' in result and len(result['choices']) > 0:
                        content = result['choices'][0]['message']['content']
                        self.print_test("API功能调用", "PASS", f"响应时间: {response_time}秒")
                        self.print_test("API响应内容", "PASS", f"收到回复: {content[:50]}...")
                        
                        # 检查token使用情况
                        if 'usage' in result:
                            usage = result['usage']
                            self.print_test("Token使用统计", "PASS", 
                                          f"输入: {usage.get('prompt_tokens', 0)}, "
                                          f"输出: {usage.get('completion_tokens', 0)}, "
                                          f"总计: {usage.get('total_tokens', 0)}")
                        
                        return True
                    else:
                        self.print_test("API功能调用", "FAIL", "响应格式异常，未找到choices字段")
                        return False
                except json.JSONDecodeError:
                    self.print_test("API功能调用", "FAIL", "响应不是有效的JSON格式")
                    return False
            else:
                self.print_test("API功能调用", "FAIL", 
                              f"HTTP状态码: {response.status_code}, 响应: {response.text[:200]}")
                return False
                
        except requests.exceptions.Timeout:
            self.print_test("API功能调用", "FAIL", "请求超时（60秒）")
            return False
        except requests.exceptions.RequestException as e:
            self.print_test("API功能调用", "FAIL", f"请求异常: {str(e)}")
            return False
    
    def test_api_performance(self) -> bool:
        """测试API性能"""
        self.print_header("API性能测试")
        
        test_payload = {
            "model": "GLM-4-Flash",
            "messages": [
                {
                    "role": "user",
                    "content": "请简单介绍一下人工智能。"
                }
            ],
            "temperature": 0.2,
            "max_tokens": 200,
            "stream": False
        }
        
        response_times = []
        success_count = 0
        
        for i in range(3):
            try:
                print(f"执行第 {i+1} 次性能测试...")
                start_time = time.time()
                response = requests.post(
                    self.base_url,
                    headers=self.headers,
                    json=test_payload,
                    timeout=60
                )
                end_time = time.time()
                response_time = round(end_time - start_time, 2)
                
                if response.status_code == 200:
                    response_times.append(response_time)
                    success_count += 1
                    print(f"  第 {i+1} 次: {response_time}秒 ✓")
                else:
                    print(f"  第 {i+1} 次: 失败 (状态码: {response.status_code}) ✗")
                
                # 避免请求过于频繁
                if i < 2:
                    time.sleep(2)
                    
            except Exception as e:
                print(f"  第 {i+1} 次: 异常 ({str(e)}) ✗")
        
        if response_times:
            avg_time = round(sum(response_times) / len(response_times), 2)
            min_time = min(response_times)
            max_time = max(response_times)
            
            self.print_test("API性能测试", "PASS", 
                          f"成功率: {success_count}/3, 平均响应时间: {avg_time}秒, "
                          f"最快: {min_time}秒, 最慢: {max_time}秒")
            return True
        else:
            self.print_test("API性能测试", "FAIL", "所有性能测试都失败了")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("智谱AI API接口测试工具")
        print("测试开始时间:", time.strftime("%Y-%m-%d %H:%M:%S"))
        
        # 执行所有测试
        network_ok = self.test_network_connectivity()
        if not network_ok:
            print("\n网络连接失败，跳过后续测试")
            self.print_summary()
            return False
        
        auth_ok = self.test_api_authentication()
        if not auth_ok:
            print("\nAPI认证失败，跳过功能测试")
            self.print_summary()
            return False
        
        function_ok = self.test_api_functionality()
        performance_ok = self.test_api_performance()
        
        self.print_summary()
        return network_ok and auth_ok and function_ok and performance_ok
    
    def print_summary(self):
        """打印测试摘要"""
        self.print_header("测试摘要")
        
        total_tests = len(self.test_results)
        passed_tests = len([r for r in self.test_results if r['status'] == 'PASS'])
        failed_tests = len([r for r in self.test_results if r['status'] == 'FAIL'])
        warning_tests = len([r for r in self.test_results if r['status'] == 'WARNING'])
        
        print(f"总测试数: {total_tests}")
        print(f"通过: {passed_tests}")
        print(f"失败: {failed_tests}")
        print(f"警告: {warning_tests}")
        
        if failed_tests == 0:
            print("\n🎉 所有测试通过！API接口工作正常。")
        else:
            print(f"\n❌ 有 {failed_tests} 个测试失败，请检查网络连接和API配置。")
        
        print("\n测试完成时间:", time.strftime("%Y-%m-%d %H:%M:%S"))
        
        # 提供故障排除建议
        if failed_tests > 0:
            self.print_troubleshooting_tips()
    
    def print_troubleshooting_tips(self):
        """打印故障排除建议"""
        self.print_header("故障排除建议")
        
        print("如果测试失败，请检查以下项目：")
        print("1. 网络连接是否正常")
        print("2. 防火墙是否阻止了HTTPS请求")
        print("3. API密钥是否有效且未过期")
        print("4. 是否有足够的API调用额度")
        print("5. 智谱AI服务是否正常运行")
        print("\n如需帮助，请联系技术支持或查看智谱AI官方文档。")


def main():
    """主函数"""
    try:
        tester = APITester()
        success = tester.run_all_tests()
        
        # 根据测试结果设置退出码
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n测试过程中发生意外错误: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()