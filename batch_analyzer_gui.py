#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
聊天记录批量分析工具 - GUI版本
调用chat_analyzer.py进行批量处理
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import os
import threading
import queue
from pathlib import Path
import sys
from datetime import datetime
import requests
import pandas as pd

# 导入我们的分析器
from chat_analyzer import ChatAnalyzer


class BatchAnalyzerGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("聊天记录批量分析工具")
        self.root.geometry("800x600")
        
        # 创建队列用于线程间通信
        self.log_queue = queue.Queue()
        
        # 存储文件列表
        self.file_list = []
        
        # 创建界面
        self.create_widgets()
        
        # 启动日志更新
        self.update_log()
    
    def create_widgets(self):
        """创建GUI界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(4, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="聊天记录批量分析工具", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 文件选择区域
        file_frame = ttk.LabelFrame(main_frame, text="文件选择", padding="10")
        file_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        file_frame.columnconfigure(1, weight=1)
        
        # 选择方式选项
        self.select_mode = tk.StringVar(value="files")
        mode_frame = ttk.Frame(file_frame)
        mode_frame.grid(row=0, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Radiobutton(mode_frame, text="选择单个文件", variable=self.select_mode, 
                       value="files", command=self.on_mode_change).pack(side=tk.LEFT, padx=(0, 20))
        ttk.Radiobutton(mode_frame, text="选择文件夹", variable=self.select_mode, 
                       value="folder", command=self.on_mode_change).pack(side=tk.LEFT)
        
        # 文件/文件夹选择按钮
        self.select_button = ttk.Button(file_frame, text="选择MD文件", 
                                       command=self.select_files)
        self.select_button.grid(row=1, column=0, padx=(0, 10))
        
        # 文件数量显示
        self.file_count_var = tk.StringVar(value="未选择文件")
        ttk.Label(file_frame, textvariable=self.file_count_var).grid(row=1, column=1, sticky=tk.W)
        
        # 清空文件列表按钮
        ttk.Button(file_frame, text="清空列表", 
                  command=self.clear_files).grid(row=1, column=2)
        
        # 配置区域
        config_frame = ttk.LabelFrame(main_frame, text="配置选项", padding="10")
        config_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        config_frame.columnconfigure(1, weight=1)
        
        # 小组长配置方式选择
        self.leader_mode = tk.StringVar(value="single")
        leader_mode_frame = ttk.Frame(config_frame)
        leader_mode_frame.grid(row=0, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Radiobutton(leader_mode_frame, text="单个小组长", variable=self.leader_mode, 
                       value="single", command=self.on_leader_mode_change).pack(side=tk.LEFT, padx=(0, 20))
        ttk.Radiobutton(leader_mode_frame, text="从TXT文件读取", variable=self.leader_mode, 
                       value="file", command=self.on_leader_mode_change).pack(side=tk.LEFT)
        
        # 小组长姓名输入
        self.leader_frame = ttk.Frame(config_frame)
        self.leader_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        self.leader_frame.columnconfigure(1, weight=1)
        
        ttk.Label(self.leader_frame, text="小组长姓名:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.leader_name_var = tk.StringVar()
        self.leader_entry = ttk.Entry(self.leader_frame, textvariable=self.leader_name_var, width=30)
        self.leader_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        
        # 小组长文件选择
        self.leader_file_frame = ttk.Frame(config_frame)
        self.leader_file_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        self.leader_file_frame.columnconfigure(1, weight=1)
        
        ttk.Label(self.leader_file_frame, text="小组长文件:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.leader_file_var = tk.StringVar()
        ttk.Entry(self.leader_file_frame, textvariable=self.leader_file_var, width=30, state="readonly").grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        ttk.Button(self.leader_file_frame, text="选择TXT文件", 
                  command=self.select_leader_file).grid(row=0, column=2)
        
        # 初始隐藏文件选择框
        self.leader_file_frame.grid_remove()
        
        # 输出目录选择
        ttk.Label(config_frame, text="输出目录:").grid(row=3, column=0, sticky=tk.W, padx=(0, 10), pady=(10, 0))
        self.output_dir_var = tk.StringVar(value="analysis_results")
        output_entry = ttk.Entry(config_frame, textvariable=self.output_dir_var, width=30)
        output_entry.grid(row=3, column=1, sticky=(tk.W, tk.E), padx=(0, 10), pady=(10, 0))
        ttk.Button(config_frame, text="浏览", 
                  command=self.select_output_dir).grid(row=3, column=2, pady=(10, 0))
        
        # 存储小组长列表和文件映射
        self.leader_list = []
        self.file_leader_mapping = {}
        
        # 控制按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, columnspan=3, pady=(0, 10))
        
        # 开始分析按钮
        self.start_button = ttk.Button(button_frame, text="开始批量分析", 
                                      command=self.start_analysis, style="Accent.TButton")
        self.start_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # 停止分析按钮
        self.stop_button = ttk.Button(button_frame, text="停止分析", 
                                     command=self.stop_analysis, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # 打开结果目录按钮
        ttk.Button(button_frame, text="打开结果目录", 
                  command=self.open_results_dir).pack(side=tk.LEFT)
        
        # 日志显示区域
        log_frame = ttk.LabelFrame(main_frame, text="处理日志", padding="10")
        log_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        # 日志文本框
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, width=80)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(main_frame, variable=self.progress_var, 
                                           maximum=100, length=400)
        self.progress_bar.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))
        
        # 状态标签
        self.status_var = tk.StringVar(value="就绪")
        status_label = ttk.Label(main_frame, textvariable=self.status_var)
        status_label.grid(row=6, column=0, columnspan=3, pady=(5, 0))
        
        # 分析控制变量
        self.is_analyzing = False
        self.should_stop = False
    
    def on_mode_change(self):
        """文件选择模式改变时的处理"""
        if self.select_mode.get() == "files":
            self.select_button.config(text="选择MD文件")
        else:
            self.select_button.config(text="选择文件夹")
        self.clear_files()
    
    def on_leader_mode_change(self):
        """小组长配置模式改变时的处理"""
        if self.leader_mode.get() == "single":
            self.leader_frame.grid()
            self.leader_file_frame.grid_remove()
        else:
            self.leader_frame.grid_remove()
            self.leader_file_frame.grid()
    
    def select_files(self):
        """选择MD文件或文件夹"""
        if self.select_mode.get() == "files":
            # 选择单个文件
            files = filedialog.askopenfilenames(
                title="选择聊天记录文件",
                filetypes=[("Markdown files", "*.md"), ("All files", "*.*")]
            )
            
            if files:
                self.file_list.extend(files)
                # 去重
                self.file_list = list(set(self.file_list))
                self.update_file_count()
                self.log_message(f"已选择 {len(files)} 个文件")
        else:
            # 选择文件夹
            folder = filedialog.askdirectory(title="选择包含MD文件的文件夹")
            if folder:
                # 扫描文件夹中的所有MD文件
                md_files = []
                for root, dirs, files in os.walk(folder):
                    for file in files:
                        if file.lower().endswith('.md'):
                            md_files.append(os.path.join(root, file))
                
                if md_files:
                    self.file_list.extend(md_files)
                    # 去重
                    self.file_list = list(set(self.file_list))
                    self.update_file_count()
                    self.log_message(f"从文件夹 {folder} 中找到 {len(md_files)} 个MD文件")
                else:
                    messagebox.showwarning("警告", "选择的文件夹中没有找到MD文件")
    
    def select_leader_file(self):
        """选择小组长名称txt文件"""
        file_path = filedialog.askopenfilename(
            title="选择小组长名称文件",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )
        
        if file_path:
            try:
                # 读取小组长名称文件
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                self.leader_list = []
                self.group_info = []  # 存储完整的小组信息
                
                # 解析格式：小组编号+小组名称·核浪+小组长名称
                # 使用正则表达式匹配模式
                import re
                
                # 匹配模式：数字+小组名称·核浪+小组长名称
                pattern = r'(\d+)([^·]+)·核浪([^0-9]+?)(?=\d+|$)'
                matches = re.findall(pattern, content)
                
                for match in matches:
                    group_number = match[0].strip()
                    group_name = match[1].strip()
                    leader_name = match[2].strip()
                    
                    # 清理小组长名称，移除可能的特殊字符和多余空格
                    leader_name = re.sub(r'[@\s]+', ' ', leader_name).strip()
                    
                    if leader_name:
                        self.leader_list.append(leader_name)
                        self.group_info.append({
                            'group_number': group_number,
                            'group_name': group_name,
                            'leader_name': leader_name,
                            'full_info': f"{group_number}{group_name}·核浪{leader_name}"
                        })
                
                self.leader_file_var.set(file_path)
                self.log_message(f"已解析 {len(self.leader_list)} 个小组长信息")
                
                # 显示解析结果
                for i, info in enumerate(self.group_info[:5]):  # 只显示前5个
                    self.log_message(f"小组{info['group_number']}: {info['group_name']} - 小组长: {info['leader_name']}")
                
                if len(self.group_info) > 5:
                    self.log_message(f"... 还有 {len(self.group_info) - 5} 个小组")
                
                # 尝试建立文件和小组长的映射关系
                self.create_file_leader_mapping()
                
            except Exception as e:
                messagebox.showerror("错误", f"读取小组长文件失败: {str(e)}")
                import traceback
                self.log_message(f"详细错误信息: {traceback.format_exc()}")
    
    def create_file_leader_mapping(self):
        """创建文件和小组长的映射关系"""
        if not self.file_list or not self.leader_list:
            return
        
        self.file_leader_mapping = {}
        matched_files = []
        unmatched_files = []
        
        # 尝试通过文件名匹配小组长
        for file_path in self.file_list:
            file_name = os.path.basename(file_path)
            matched_leader = None
            matched_info = None
            
            # 优先尝试匹配完整的小组长名称
            for info in self.group_info:
                leader_name = info['leader_name']
                # 尝试匹配小组长名称的各个部分
                if leader_name in file_name:
                    matched_leader = leader_name
                    matched_info = info
                    break
                
                # 尝试匹配小组长名称的主要部分（去掉特殊字符后）
                clean_leader = leader_name.split('-')[0].split('\\')[0].strip()
                if clean_leader and clean_leader in file_name:
                    matched_leader = leader_name
                    matched_info = info
                    break
            
            # 如果还没匹配到，尝试匹配小组编号
            if not matched_leader:
                for info in self.group_info:
                    group_number = info['group_number']
                    # 尝试将group_number转换为整数进行格式化，如果失败则直接使用字符串
                    try:
                        group_num_int = int(group_number)
                        formatted_group = f"{group_num_int:02d}"  # 格式化为两位数，如01, 02
                        if formatted_group in file_name or group_number in file_name:
                            matched_leader = info['leader_name']
                            matched_info = info
                            break
                    except ValueError:
                        # 如果不能转换为整数，直接使用字符串匹配
                        if group_number in file_name:
                            matched_leader = info['leader_name']
                            matched_info = info
                            break
            
            if matched_leader:
                self.file_leader_mapping[file_path] = matched_leader
                matched_files.append((file_name, matched_info))
            else:
                # 如果没有匹配到，使用第一个小组长作为默认值
                self.file_leader_mapping[file_path] = self.leader_list[0]
                unmatched_files.append(file_name)
        
        # 显示匹配结果
        self.log_message(f"文件匹配结果: {len(matched_files)} 个文件成功匹配，{len(unmatched_files)} 个文件使用默认小组长")
        
        # 显示匹配详情
        if matched_files:
            self.log_message("成功匹配的文件:")
            for file_name, info in matched_files[:3]:  # 只显示前3个
                self.log_message(f"  {file_name} → 小组{info['group_number']}: {info['leader_name']}")
            if len(matched_files) > 3:
                self.log_message(f"  ... 还有 {len(matched_files) - 3} 个文件成功匹配")
        
        if unmatched_files:
            self.log_message(f"未匹配文件(使用默认小组长 {self.leader_list[0]}):")
            for file_name in unmatched_files[:3]:  # 只显示前3个
                self.log_message(f"  {file_name}")
            if len(unmatched_files) > 3:
                self.log_message(f"  ... 还有 {len(unmatched_files) - 3} 个未匹配文件")
    
    def clear_files(self):
        """清空文件列表"""
        self.file_list.clear()
        self.update_file_count()
        self.log_message("已清空文件列表")
    
    def update_file_count(self):
        """更新文件数量显示"""
        count = len(self.file_list)
        if count == 0:
            self.file_count_var.set("未选择文件")
        else:
            self.file_count_var.set(f"已选择 {count} 个文件")
    
    def select_output_dir(self):
        """选择输出目录"""
        directory = filedialog.askdirectory(title="选择输出目录")
        if directory:
            self.output_dir_var.set(directory)
    
    def log_message(self, message):
        """添加日志消息到队列"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_queue.put(f"[{timestamp}] {message}")
    
    def update_log(self):
        """更新日志显示"""
        try:
            while True:
                message = self.log_queue.get_nowait()
                self.log_text.insert(tk.END, message + "\n")
                self.log_text.see(tk.END)
        except queue.Empty:
            pass
        
        # 每100ms检查一次
        self.root.after(100, self.update_log)
    
    def start_analysis(self):
        """开始批量分析"""
        # 验证输入
        if not self.file_list:
            messagebox.showerror("错误", "请先选择要分析的文件")
            return
        
        # 根据小组长配置模式验证输入
        if self.leader_mode.get() == "single":
            leader_name = self.leader_name_var.get().strip()
            if not leader_name:
                messagebox.showerror("错误", "请输入小组长姓名")
                return
        else:
            if not self.leader_list:
                messagebox.showerror("错误", "请先选择小组长名称文件")
                return
            
            # 如果选择了文件夹模式但没有建立映射，尝试重新建立
            if not self.file_leader_mapping:
                self.create_file_leader_mapping()
        
        # 更新界面状态
        self.is_analyzing = True
        self.should_stop = False
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        self.status_var.set("正在分析...")
        self.progress_var.set(0)
        
        # 清空日志
        self.log_text.delete(1.0, tk.END)
        
        # 在新线程中执行分析
        analysis_thread = threading.Thread(target=self.run_analysis, daemon=True)
        analysis_thread.start()
    
    def stop_analysis(self):
        """停止分析"""
        self.should_stop = True
        self.log_message("正在停止分析...")
    
    def run_analysis(self):
        """执行批量分析"""
        try:
            output_dir = self.output_dir_var.get().strip()
            
            # 确保输出目录存在
            os.makedirs(output_dir, exist_ok=True)
            
            total_files = len(self.file_list)
            successful = 0
            failed = 0
            
            self.log_message(f"开始批量分析 {total_files} 个文件")
            
            # 根据小组长配置模式显示不同信息
            if self.leader_mode.get() == "single":
                leader_name = self.leader_name_var.get().strip()
                self.log_message(f"小组长姓名: {leader_name}")
            else:
                self.log_message(f"使用小组长文件: {len(self.leader_list)} 个小组长")
                self.log_message(f"文件映射: {len(self.file_leader_mapping)} 个文件已映射")
            
            self.log_message(f"输出目录: {output_dir}")
            self.log_message("-" * 50)
            
            # 创建分析器实例
            api_key = "e601f05d308a42be8d5f3e40a98ea324.aPTWcglZd4DljsUY"
            analyzer = ChatAnalyzer(api_key)
            
            for i, file_path in enumerate(self.file_list):
                if self.should_stop:
                    self.log_message("分析已被用户停止")
                    break
                
                try:
                    file_name = os.path.basename(file_path)
                    
                    # 根据配置模式确定当前文件的小组长
                    if self.leader_mode.get() == "single":
                        current_leader = self.leader_name_var.get().strip()
                    else:
                        current_leader = self.file_leader_mapping.get(file_path, self.leader_list[0])
                    
                    self.log_message(f"正在分析文件 {i+1}/{total_files}: {file_name} (小组长: {current_leader})")
                    
                    # 更新进度
                    progress = (i / total_files) * 100
                    self.progress_var.set(progress)
                    
                    # 读取原始聊天记录用于统计分析
                    original_chat_content = analyzer.read_markdown_file(file_path)
                    
                    # 分析聊天统计数据
                    chat_statistics = analyzer.analyze_chat_statistics(original_chat_content, current_leader)
                    analyzer.chat_statistics = chat_statistics
                    
                    # 执行分析
                    result = analyzer.analyze_chat(file_path, current_leader)
                    
                    # 保存分析结果（使用chat_analyzer.py中的默认逻辑）
                    analyzer.save_result(result, current_leader, None, file_path)
                    
                    # 提取数据并生成表格
                    if hasattr(analyzer, 'segment_files') and analyzer.segment_files:
                        extracted_data = analyzer.extract_data_from_segment_files(current_leader)
                    else:
                        extracted_data = analyzer.extract_data_from_markdown(result, current_leader)
                    
                    if extracted_data.get('leader_actions') or extracted_data.get('key_moments'):
                        # 创建表格报告（使用chat_analyzer.py中的默认逻辑）
                        table_report = analyzer.create_summary_statistics(extracted_data, current_leader)
                        table_report += analyzer.create_leader_actions_table(extracted_data.get('leader_actions', []))
                        table_report += analyzer.create_key_moments_table(extracted_data.get('key_moments', []))
                        
                        # 保存表格报告（使用chat_analyzer.py中的默认逻辑）
                        chat_base_name = analyzer.get_chat_base_name(file_path)
                        chat_folder = os.path.join(output_dir, chat_base_name)
                        if not os.path.exists(chat_folder):
                            os.makedirs(chat_folder)
                        
                        clean_leader_name = analyzer.sanitize_filename(current_leader)
                        table_output_file = os.path.join(chat_folder, f"table_report_{clean_leader_name}.md")
                        with open(table_output_file, 'w', encoding='utf-8') as f:
                            f.write(table_report)
                        
                        # 保存分段文件（如果有的话）
                        if hasattr(analyzer, 'segment_files') and analyzer.segment_files:
                            for segment_name, segment_file, segment_content in analyzer.segment_files:
                                segment_output_file = os.path.join(chat_folder, f"segment_{clean_leader_name}_{segment_name}.md")
                                with open(segment_output_file, 'w', encoding='utf-8') as f:
                                    f.write(segment_content)
                        
                        # 保存Excel格式的表格
                        excel_file = analyzer.save_excel_tables(extracted_data, current_leader, file_path)
                        
                        # 从返回的文件路径中提取文件名
                        if excel_file:
                            excel_filename = os.path.basename(excel_file)
                        else:
                            excel_filename = "Excel文件生成失败"
                        
                        self.log_message(f"  └─ 文件夹: {chat_base_name}/")
                        self.log_message(f"  └─ Excel: {excel_filename}")
                    
                    successful += 1
                    self.log_message(f"✓ {file_name} 分析完成 (小组长: {current_leader})")
                    
                except requests.exceptions.Timeout as e:
                    failed += 1
                    self.log_message(f"✗ {file_name} 分析失败: API调用超时")
                    self.log_message(f"  建议: 网络连接不稳定或文件内容过长，可稍后重试")
                    
                except requests.exceptions.RequestException as e:
                    failed += 1
                    self.log_message(f"✗ {file_name} 分析失败: 网络请求错误")
                    self.log_message(f"  错误详情: {str(e)}")
                    
                except Exception as e:
                    failed += 1
                    self.log_message(f"✗ {file_name} 分析失败: {str(e)}")
                    # 如果是其他类型的错误，显示更详细的信息
                    if "API调用失败" in str(e):
                        self.log_message(f"  建议: 检查网络连接或稍后重试")
            
            # 更新最终进度
            self.progress_var.set(100)
            
            # 显示结果摘要
            self.log_message("-" * 50)
            self.log_message(f"批量分析完成!")
            self.log_message(f"成功: {successful} 个文件")
            self.log_message(f"失败: {failed} 个文件")
            self.log_message(f"结果保存在: {output_dir}")
            
            if successful > 0:
                messagebox.showinfo("完成", f"批量分析完成!\n成功: {successful} 个文件\n失败: {failed} 个文件")
            
        except Exception as e:
            self.log_message(f"批量分析出错: {str(e)}")
            messagebox.showerror("错误", f"批量分析出错: {str(e)}")
        
        finally:
            # 恢复界面状态
            self.is_analyzing = False
            self.start_button.config(state=tk.NORMAL)
            self.stop_button.config(state=tk.DISABLED)
            self.status_var.set("就绪")
    

    def open_results_dir(self):
        """打开结果目录"""
        output_dir = self.output_dir_var.get().strip()
        if os.path.exists(output_dir):
            if sys.platform == "win32":
                os.startfile(output_dir)
            elif sys.platform == "darwin":
                os.system(f"open '{output_dir}'")
            else:
                os.system(f"xdg-open '{output_dir}'")
        else:
            messagebox.showwarning("警告", "输出目录不存在")


def main():
    """主函数"""
    root = tk.Tk()
    
    # 设置主题样式
    style = ttk.Style()
    if "vista" in style.theme_names():
        style.theme_use("vista")
    elif "clam" in style.theme_names():
        style.theme_use("clam")
    
    # 创建应用
    app = BatchAnalyzerGUI(root)
    
    # 运行主循环
    root.mainloop()


if __name__ == "__main__":
    main()