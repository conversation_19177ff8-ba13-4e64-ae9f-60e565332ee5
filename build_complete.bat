@echo off
chcp 65001 >nul
echo Simple Build with py launcher
echo =============================
echo.

echo Checking project files...
if not exist "batch_analyzer_gui.py" (
    echo ERROR: batch_analyzer_gui.py not found in current directory
    echo Current directory: %CD%
    echo.
    echo Please make sure you are running this script from the project folder
    echo that contains batch_analyzer_gui.py
    echo.
    pause
    exit /b 1
)
echo ✓ Found batch_analyzer_gui.py
echo.

echo Using py launcher...
py --version
if %errorlevel% neq 0 (
    echo ERROR: py launcher failed
    echo Please check Python installation
    pause
    exit /b 1
)
echo Python check completed successfully
pause

echo.
echo Installing dependencies...
echo Step 1: Upgrading pip...
py -m pip install --upgrade pip -i https://pypi.tuna.tsinghua.edu.cn/simple
if %errorlevel% neq 0 (
    echo ERROR: Failed to upgrade pip
    pause
    exit /b 1
)
echo Pip upgrade completed
pause

echo.
echo Step 2: Installing required packages...
py -m pip install pyinstaller requests pandas openpyxl -i https://pypi.tuna.tsinghua.edu.cn/simple
if %errorlevel% neq 0 (
    echo ERROR: Failed to install packages
    pause
    exit /b 1
)
echo Package installation completed
pause

echo.
echo Building application...
echo This may take several minutes, please wait...
py -m PyInstaller --onefile --windowed --name="ChatLogAnalyzer" batch_analyzer_gui.py
if %errorlevel% neq 0 (
    echo ERROR: Build process failed
    pause
    exit /b 1
)
echo Build process completed
pause

echo.
echo Renaming to Chinese filename...
if exist "dist\ChatLogAnalyzer.exe" (
    ren "dist\ChatLogAnalyzer.exe" "聊天记录分析工具.exe"
    if exist "dist\聊天记录分析工具.exe" (
        echo ✓ Successfully renamed to Chinese filename
    ) else (
        echo ! Rename failed, keeping English filename: ChatLogAnalyzer.exe
    )
) else (
    echo ERROR: ChatLogAnalyzer.exe not found
)
pause

echo.
echo Checking results...
if exist "dist\聊天记录分析工具.exe" (
    echo ========================================
    echo ✓ SUCCESS! File created successfully!
    echo ========================================
    echo Location: dist\聊天记录分析工具.exe
    echo.
    for %%i in ("dist\聊天记录分析工具.exe") do echo File size: %%~zi bytes
) else if exist "dist\ChatLogAnalyzer.exe" (
    echo ========================================
    echo ✓ SUCCESS! File created successfully!
    echo ========================================
    echo Location: dist\ChatLogAnalyzer.exe
    echo Note: Using English filename due to encoding issues
    echo.
    for %%i in ("dist\ChatLogAnalyzer.exe") do echo File size: %%~zi bytes
) else (
    echo ========================================
    echo ✗ BUILD FAILED!
    echo ========================================
    echo The executable file was not created.
    echo Please check the error messages above.
)

echo.
echo Build process finished!
echo Press any key to exit...
pause


