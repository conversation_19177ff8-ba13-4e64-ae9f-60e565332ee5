# 群聊记录分析工具

这个工具使用智谱AI的GLM-4.5-Flash模型来分析群聊记录中小组长的关键动作和行为模式。

## 功能特点

- 📊 结构化分析小组长的关键动作和关键时间节点
- 🎯 智能提取任务分配、会议组织、决策制定等关键行为
- 📝 自动去重，避免重复数据
- 💾 生成Excel表格，包含详细统计数据
- 🔄 支持单文件和批量处理
- 🖥️ 提供GUI界面，操作更友好

## 安装依赖

```bash
pip install requests pandas openpyxl tkinter
```

## 使用方法

### 方法1: 单文件处理（命令行）

```bash
python chat_analyzer.py
```

程序会提示你输入：
1. 聊天记录的markdown文件路径
2. 小组长姓名
3. 是否需要自定义输出文件路径（可选）

### 方法2: 批量处理（GUI界面）

```bash
python batch_analyzer_gui.py
```

GUI界面功能：
- 📁 **灵活文件选择**: 
  - 选择单个MD文件进行批量分析
  - 选择整个文件夹，自动扫描所有MD文件
- 👥 **智能小组长配置**:
  - 单个小组长模式：所有文件使用同一个小组长
  - TXT文件模式：从文件中读取多个小组长名称，自动匹配文件
- ⚙️ **配置选项**: 设置输出目录
- 📊 **实时日志**: 显示处理进度和结果
- 📈 **进度条**: 可视化显示分析进度
- 🛑 **停止功能**: 可以随时停止分析过程
- 📂 **快速打开**: 一键打开结果目录

### 使用示例

#### 单文件处理
```
==================================================
群聊记录分析工具
==================================================
请输入聊天记录的markdown文件路径: chat_log.md
请输入小组长姓名: 张三
是否需要自定义输出文件路径？(y/n，默认为n): n
```

#### 批量处理 - 基本模式
1. 运行 `python batch_analyzer_gui.py`
2. 选择"选择单个文件"模式，点击"选择MD文件"选择多个聊天记录文件
3. 选择"单个小组长"模式，输入小组长姓名
4. 点击"开始批量分析"
5. 等待处理完成，查看结果

#### 批量处理 - 文件夹模式
1. 运行 `python batch_analyzer_gui.py`
2. 选择"选择文件夹"模式，选择包含所有MD文件的文件夹
3. 程序会自动扫描文件夹中的所有.md文件
4. 配置小组长信息，点击"开始批量分析"

#### 批量处理 - 多小组长模式
1. 准备小组长名称TXT文件（参考 `leaders_example.txt`）
2. 运行 `python batch_analyzer_gui.py`
3. 选择文件或文件夹
4. 选择"从TXT文件读取"模式，选择小组长名称文件
5. 程序会自动尝试匹配文件名和小组长名称
6. 点击"开始批量分析"

## 分析维度

### 小组长关键动作分析
- **任务分配和安排**: @某人做某事、分配工作
- **会议或活动组织**: 通知开会、安排活动
- **重要决策制定**: 做出重要决定、确定方案
- **问题解决和指导**: 回答问题、提供指导
- **团队协调和管理**: 协调冲突、管理进度
- **项目进度跟踪**: 询问进度、检查完成情况
- **资源分配**: 分配资源、调配人员
- **重要通知和公告**: 发布重要信息、政策变更

### 关键时间节点识别
- **活动节点**: 会议开始/结束、课程开始/结束
- **讨论高峰**: 多人积极参与讨论的时段
- **积极发言**: 学员发言频率高的时间段
- **重要公告**: 重要信息发布时刻
- **问题解决**: 问题得到解决的关键时刻
- **决策达成**: 达成共识或做出决定的时间点
- **项目里程碑**: 重要节点完成
- **话题转换**: 讨论主题发生变化的时刻
- **情绪高涨**: 群体情绪激动或兴奋的时刻

## 小组长名称TXT文件格式

当使用"从TXT文件读取"模式时，需要准备一个包含小组信息的文本文件。格式为：`小组编号+小组名称·核浪+小组长名称`

```txt
# 小组长名称列表 - 格式：小组编号+小组名称·核浪+小组长名称
# 程序会自动解析出小组长名称进行匹配

01浪击千钧·核浪🐳庞煜\-学习专业课
06万物有灵·核浪小喜\-山西\-旅管\-阅读 旅行@树成林
13鲜潮将至·核浪🌙下月学姐@树成林
02破浪前行·核浪张三\-项目管理
03乘风破浪·核浪李四\-技术开发
```

### 智能匹配规则
程序会使用多种策略自动匹配文件和小组长：

1. **小组长姓名匹配**: 在文件名中查找完整的小组长姓名
   - 例如：`聊天记录_庞煜_20231201.md` → `🐳庞煜\-学习专业课`

2. **小组长主要名称匹配**: 匹配小组长姓名的主要部分
   - 例如：`chat_庞煜.md` → `🐳庞煜\-学习专业课`

3. **小组编号匹配**: 通过小组编号进行匹配
   - 例如：`01组聊天记录.md` → `🐳庞煜\-学习专业课`（小组01）

4. **默认匹配**: 如果以上都没匹配到，使用第一个小组长作为默认值

### 匹配结果显示
程序会在日志中显示详细的匹配结果：
```
文件匹配结果: 8 个文件成功匹配，2 个文件使用默认小组长
成功匹配的文件:
  chat_庞煜_20231201.md → 小组01: 🐳庞煜\-学习专业课
  聊天记录_张三.md → 小组02: 张三\-项目管理
  ...
```

## 输出格式

### 生成的文件
每次分析会生成以下文件：
1. `analysis_result_[文件名]_[小组长].md` - 原始分析报告
2. `table_report_[文件名]_[小组长].md` - 表格格式报告
3. `analysis_tables_[文件名]_[小组长].xlsx` - Excel数据表格
4. `segment_[文件名]_[小组长]_[时间段].md` - 分段分析报告（如适用）

### Excel表格结构
Excel文件包含三个工作表：

#### 工作表1: 小组长关键动作记录表
| 时间 | 小组长姓名 | 动作类型 | 具体动作描述 | 上下文背景 | 涉及人员 | 时间段 |
|------|------------|----------|--------------|------------|----------|--------|

#### 工作表2: 关键时间节点记录表
| 时间 | 事件类型 | 事件描述 | 参与人员 | 持续时间(分钟) | 时间段 |
|------|----------|----------|----------|----------------|--------|

#### 工作表3: 详细统计摘要
| 统计项目 | 统计结果 |
|----------|----------|
| 群聊总聊天数 | X条消息 |
| 小组长发言次数 | X次 |
| 群聊活跃人数 | X人 |
| 小组长参与度 | X% |
| 用户发言排行榜 | TOP 10 |

## 注意事项

1. 确保聊天记录文件为UTF-8编码的Markdown格式
2. 小组长姓名需要与聊天记录中的显示名称完全一致
3. 分析结果基于聊天记录内容，请确保数据的完整性和准确性
4. API调用需要网络连接，请确保网络畅通

## 错误处理

- 文件不存在：检查文件路径是否正确
- API调用失败：检查网络连接和API密钥
- 编码错误：确保文件为UTF-8编码

## 技术支持

如有问题，请检查：
1. Python版本（建议3.7+）
2. 依赖包是否正确安装
3. 文件路径和权限
4. 网络连接状态