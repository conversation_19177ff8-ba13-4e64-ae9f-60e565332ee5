#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
群聊记录分析工具 - 分析小组长关键动作
使用智谱AI GLM-4.5-Flash模型
"""

import json
import requests
import sys
import os
import re
from typing import Dict, Any, List
from pathlib import Path
import pandas as pd


class ChatAnalyzer:
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://open.bigmodel.cn/api/paas/v4/chat/completions"
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
    
    def read_markdown_file(self, file_path: str) -> str:
        """读取markdown文件内容"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except FileNotFoundError:
            print(f"错误: 找不到文件 {file_path}")
            sys.exit(1)
        except Exception as e:
            print(f"错误: 读取文件失败 - {e}")
            sys.exit(1)
    
    def create_analysis_prompt(self, chat_content: str, leader_name: str) -> str:
        """创建分析提示词"""
        prompt = f"""
你是一个专业的聊天记录分析助手。请分析以下群聊记录，重点识别和标记小组长"{leader_name}"发布的各类活动和关键动作。
禁止你虚构数据或是输出重复数据。

**重要分析要求：**
1. **活动识别重点**：特别关注小组长发起的接龙活动、打卡活动、投票活动、学习活动、会议组织等
2. **活动执行跟踪**：记录活动的发起、执行过程、参与情况和结果
3. **群聊内容概括**：对于小组长与组员的日常交流，进行合理概括，突出关键信息
4. **时间准确性**：绝对不允许虚造内容和时间，所有信息必须基于实际聊天记录

**聊天记录格式说明：**
- 每条消息格式为：**时间 用户名**: 消息内容
- 例如：**15:57:00 小组长**: 大家开始接龙打卡

**活动类型识别指南：**
- 接龙活动：小组长发起"接龙"、"跟上"、"继续"、"速度接龙"等，需记录参与人数和完成情况
- 打卡活动：涉及"打卡"、"签到"、"完成情况"、"课程打卡"等，需统计参与率
- 投票活动：发起"投票"、"选择"、"表决"、"群投票"等，需记录投票主题和结果
- 学习活动：组织"学习"、"分享"、"讨论"、"作业评优"等，需记录活动内容和效果
- 会议活动：安排"会议"、"开会"、"集合"等，需记录会议主题和参与情况
- 任务分配：@某人、分配工作、布置任务、"军令状"等，需明确任务内容和责任人
- 制度发布：发布"积分制度"、"规则"、"奖励机制"等，需记录制度内容和影响范围
- 信息转发：转发重要信息、推优信息、通知等，需概括转发内容的重要性

**群聊内容概括原则：**
- 对于小组长的日常问候、鼓励话语等，可概括为"日常交流和团队激励"
- 对于小组长回复组员问题的内容，重点记录问题类型和解决方案
- 对于小组长与组员的互动讨论，重点提取关键信息和决策要点
- 对于活动相关的群聊，必须详细记录活动的完整流程和参与情况

请按照以下格式输出分析结果，重点关注小组长的具体动作和群内关键事件：

# {leader_name} 群聊分析报告

## 一、小组长关键动作记录表

请按照以下格式逐条列出小组长的每个关键动作：

**格式说明：**
时间戳 | 小组长姓名 | 动作类型 | 描述 | 上下文 | 参与者

**动作类型分类：**
- 重要通知：发布重要信息、制度公告、活动通知等
- 任务分配：@某人做某事、分配具体工作、布置任务等  
- 进度跟踪：检查完成情况、催促进度、跟进任务等
- 活动安排：组织接龙、投票、会议、学习活动等
- 指导：回答问题、提供建议、解决困难等
- 团队激励：表扬、鼓励、正面反馈等

**示例格式：**
20:05:14 | {leader_name} | 重要通知 | 组织课程打卡活动，通知所有人进行课程打卡 | 消息内容: @所有人 课程打卡，课程打卡 | 全体成员

请严格按照此格式列出所有小组长的关键动作，每行一个动作。

## 二、关键时间节点记录表

请按照以下格式逐条列出群聊中的关键时间节点：

**格式说明：**
时间戳 | 事件类型 | 描述 | 参与者 | 持续时间(分钟)

**事件类型分类：**
- 重要通知：重要信息发布、制度公告等
- 活动启动：接龙开始、投票开始、会议开始等
- 讨论高峰期：多人积极参与讨论的时段
- 问题解决：问题得到解决的关键时刻
- 情绪高峰：群体情绪激动或兴奋的时刻
- 话题转换：讨论主题发生重要变化
- 成果展示：作业展示、成绩公布等

**示例格式：**
20:05:14 | 重要通知 | 课程打卡活动正式开始，小组长发布打卡通知 | {leader_name}, 全体成员 | 

请严格按照此格式列出所有关键时间节点，每行一个事件。

## 三、统计摘要

- **分析时间段**: [根据聊天记录确定时间范围]
- **小组长总发言次数**: X次
- **关键动作总数**: X个
- **关键时间节点总数**: X个
- **最活跃时间段**: XX:XX - XX:XX
- **主要活动类型**: [总结主要的活动类型]
- **活动参与度**: [评估成员参与活动的积极性]
- **管理效果**: [评估小组长的管理效果]

聊天记录：
{chat_content}
"""
        return prompt
    
    def call_glm_api(self, prompt: str, max_retries: int = 3) -> Dict[Any, Any]:
        """调用GLM-4.5-Flash API，带重试机制"""
        payload = {
            "model": "GLM-4-Flash",
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "temperature": 0.2,
            "max_tokens": 5000,
            "stream": False,
        }
        
        for attempt in range(max_retries):
            try:
                print(f"正在调用API (尝试 {attempt + 1}/{max_retries})...")
                response = requests.post(
                    self.base_url,
                    headers=self.headers,
                    json=payload,
                    timeout=120  # 增加超时时间到120秒
                )
                response.raise_for_status()
                return response.json()
                
            except requests.exceptions.Timeout as e:
                print(f"API调用超时 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    wait_time = (attempt + 1) * 10  # 递增等待时间：10s, 20s, 30s
                    print(f"等待 {wait_time} 秒后重试...")
                    import time
                    time.sleep(wait_time)
                else:
                    print("所有重试都失败了，跳过此文件")
                    raise
                    
            except requests.exceptions.RequestException as e:
                print(f"API调用失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    wait_time = (attempt + 1) * 5  # 递增等待时间：5s, 10s, 15s
                    print(f"等待 {wait_time} 秒后重试...")
                    import time
                    time.sleep(wait_time)
                else:
                    print("所有重试都失败了，跳过此文件")
                    raise
    
    def split_chat_by_time(self, chat_content: str) -> List[Dict[str, str]]:
        """按时间节点分割聊天记录 - 确保总是返回分段结果"""
        print("正在按时间节点分割聊天记录...")
        
        # 按行分割内容
        lines = chat_content.split('\n')
        
        # 定义时间段
        segments = [
            {"name": "上午时段", "start": "00:00:00", "end": "12:00:00", "content": []},
            {"name": "下午时段", "start": "12:00:00", "end": "18:00:00", "content": []},
            {"name": "晚上时段", "start": "18:00:00", "end": "23:59:59", "content": []}
        ]
        
        # 时间戳正则表达式
        time_pattern = r'(\d{1,2}):(\d{2}):(\d{2})'
        
        for line in lines:
            if line.strip():
                # 尝试匹配时间格式
                match = re.search(time_pattern, line)
                if match:
                    hour = int(match.group(1))
                    minute = int(match.group(2))
                    time_minutes = hour * 60 + minute
                    
                    # 判断属于哪个时间段
                    if time_minutes < 12 * 60:  # 12:00之前
                        segments[0]["content"].append(line)
                    elif time_minutes < 18 * 60:  # 18:00之前
                        segments[1]["content"].append(line)
                    else:  # 18:00之后
                        segments[2]["content"].append(line)
                else:
                    # 非时间格式的行，添加到最后一个非空段落
                    for segment in reversed(segments):
                        if segment["content"]:
                            segment["content"].append(line)
                            break
                    else:
                        # 如果所有段落都为空，添加到第一个段落
                        segments[0]["content"].append(line)
        
        # 转换为字符串格式，包括空段落
        result_segments = []
        for segment in segments:
            content_str = '\n'.join(segment["content"]) if segment["content"] else ""
            message_count = len([line for line in segment["content"] if re.search(time_pattern, line)]) if segment["content"] else 0
            
            # 包括所有段落，即使消息数量为0
            result_segments.append({
                "name": segment["name"],
                "time_range": f"{segment['start']} - {segment['end']}",
                "content": content_str,
                "message_count": message_count
            })
            
            if message_count == 0:
                print(f"{segment['name']} - 消息数量为0，将创建空分析结果")
            else:
                print(f"{segment['name']} - {message_count} 条消息")
        
        print(f"文件已分割为 {len(result_segments)} 个时间段（包括空段落）")
        return result_segments
    
    def analyze_chat_segment(self, segment: Dict[str, str], leader_name: str, segment_index: int) -> str:
        """分析单个聊天记录段落"""
        print(f"正在分析第 {segment_index} 段: {segment['name']}...")
        
        prompt = self.create_analysis_prompt(segment['content'], leader_name)
        
        print(f"正在调用GLM-4.5-Flash模型处理 {segment['name']}...")
        response = self.call_glm_api(prompt)
        
        if 'choices' in response and len(response['choices']) > 0:
            analysis_result = response['choices'][0]['message']['content']
            return analysis_result
        else:
            print(f"错误: {segment['name']} API返回格式异常")
            print(f"响应内容: {response}")
            return f"{{\"leader_actions\": [], \"key_moments\": [], \"error\": \"API调用失败\"}}"
    
    def merge_analysis_results(self, results: List[str], segments: List[Dict[str, str]], leader_name: str, markdown_file: str = None) -> str:
        """合并多个分析结果并保存分段文件"""
        print("正在合并分析结果...")
        
        # 创建默认保存目录
        default_dir = "analysis_results"
        if not os.path.exists(default_dir):
            os.makedirs(default_dir)
        
        # 如果提供了markdown_file，创建以聊天记录命名的文件夹
        if markdown_file:
            chat_base_name = self.get_chat_base_name(markdown_file)
            chat_folder = os.path.join(default_dir, chat_base_name)
            if not os.path.exists(chat_folder):
                os.makedirs(chat_folder)
            segment_save_dir = chat_folder
        else:
            segment_save_dir = default_dir
        
        clean_leader_name = self.sanitize_filename(leader_name)
        
        # 保存每个时间段的独立MD文件
        segment_files = []
        for i, result in enumerate(results):
            segment_name = segments[i]['name']
            segment_time_range = segments[i]['time_range']
            message_count = segments[i]['message_count']
            
            print(f"正在保存 {segment_name} 的分析结果...")
            
            # 创建单独的时间段报告
            segment_report = f"# {leader_name} - {segment_name} 分析报告\n\n"
            segment_report += f"## 基本信息\n\n"
            segment_report += f"- **时间段**: {segment_name} ({segment_time_range})\n"
            segment_report += f"- **消息数量**: {message_count} 条\n\n"
            segment_report += result + "\n\n"
            
            # 保存单独的MD文件
            segment_file = os.path.join(segment_save_dir, f"segment_{clean_leader_name}_{segment_name}.md")
            try:
                with open(segment_file, 'w', encoding='utf-8') as f:
                    f.write(segment_report)
                print(f"{segment_name} 分析结果已保存到: {segment_file}")
                segment_files.append((segment_name, segment_file, segment_report))
            except Exception as e:
                print(f"保存 {segment_name} 文件失败: {e}")
        
        # 创建合并后的报告
        merged_report = f"# {leader_name} 群聊分析报告（完整版）\n\n"
        merged_report += f"## 分析概述\n\n"
        merged_report += f"- **分析时间段数**: {len(results)}个\n"
        merged_report += f"- **处理的时间段**: {', '.join([seg['name'] for seg in segments])}\n\n"
        
        # 合并各个时间段的分析结果
        for i, result in enumerate(results):
            segment_name = segments[i]['name']
            segment_time_range = segments[i]['time_range']
            message_count = segments[i]['message_count']
            
            merged_report += f"---\n\n"
            merged_report += f"## {segment_name} ({segment_time_range})\n\n"
            merged_report += f"**消息数量**: {message_count} 条\n\n"
            merged_report += result + "\n\n"
        
        print(f"合并完成: 已整合 {len(results)} 个时间段的分析结果")
        
        # 将分段文件信息存储到类属性中，供后续使用
        self.segment_files = segment_files
        
        return merged_report
    
    def extract_data_from_segment_files(self, leader_name: str) -> Dict[str, Any]:
        """从分段MD文件中提取数据并整合"""
        print("正在从分段MD文件中提取数据...")
        
        all_actions = []
        all_moments = []
        all_stats = {}
        
        # 检查是否有分段文件
        if not hasattr(self, 'segment_files') or not self.segment_files:
            print("没有找到分段文件，使用默认提取方法")
            return {"leader_actions": [], "key_moments": [], "statistics": {}, "leader_name": leader_name}
        
        # 处理每个分段文件
        for segment_name, segment_file, segment_content in self.segment_files:
            print(f"正在处理 {segment_name} 的数据...")
            
            # 从分段内容中提取数据
            segment_actions = self.extract_actions_from_segment_content(segment_content, segment_name)
            segment_moments = self.extract_moments_from_segment_content(segment_content, segment_name)
            
            all_actions.extend(segment_actions)
            all_moments.extend(segment_moments)
            
            print(f"从 {segment_name} 提取到 {len(segment_actions)} 个动作, {len(segment_moments)} 个时间节点")
        
        # 按时间排序
        all_actions.sort(key=lambda x: x.get('timestamp', '00:00:00'))
        all_moments.sort(key=lambda x: x.get('timestamp', '00:00:00'))
        
        # 生成统计信息
        all_stats = {
            'total_actions': len(all_actions),
            'total_moments': len(all_moments),
            'segments_processed': len(self.segment_files),
            'analysis_period': self.get_time_range(all_actions + all_moments)
        }
        
        result = {
            "leader_actions": all_actions,
            "key_moments": all_moments,
            "statistics": all_stats,
            "leader_name": leader_name
        }
        
        print(f"整合完成: 总共 {len(all_actions)} 个关键动作, {len(all_moments)} 个关键时间节点")
        return result
    
    def extract_actions_from_segment_content(self, content: str, segment_name: str) -> List[Dict]:
        """从分段内容中提取小组长动作"""
        actions = []
        seen_actions = set()  # 用于去重
        
        # 查找小组长关键动作记录表
        table_patterns = [
            r"## 一、小组长关键动作记录表\s*\n(.*?)(?=## 二、|## 三、|$)",
            r"(\|[^|]*时间戳[^|]*\|[^|]*小组长姓名[^|]*\|.*?)(?=\n## |\n---|\n\n## |$)"
        ]
        
        for pattern in table_patterns:
            matches = re.findall(pattern, content, re.DOTALL)
            
            for table_content in matches:
                # 使用正则表达式提取表格行
                line_pattern = r'(\d{2}:\d{2}:\d{2})\s*\|\s*([^|]+)\s*\|\s*([^|]+)\s*\|\s*([^|]+)\s*\|\s*([^|]+)\s*\|\s*([^|]+)'
                line_matches = re.findall(line_pattern, table_content)
                
                for match in line_matches:
                    timestamp, leader_name, action_type, description, context, participants = match
                    
                    # 创建唯一标识符用于去重
                    unique_key = f"{timestamp.strip()}|{leader_name.strip()}|{action_type.strip()}|{description.strip()}"
                    
                    if unique_key not in seen_actions:
                        seen_actions.add(unique_key)
                        action = {
                            "timestamp": timestamp.strip(),
                            "leader_name": leader_name.strip(),
                            "action_type": action_type.strip(),
                            "action_category": action_type.strip(),
                            "description": description.strip(),
                            "participants": participants.strip(),
                            "context": context.strip(),
                            "segment": segment_name
                        }
                        actions.append(action)
                
                # 如果正则匹配失败，尝试标准Markdown表格格式
                if not line_matches:
                    table_lines = table_content.split('\n')
                    in_table = False
                    
                    for line in table_lines:
                        line = line.strip()
                        if not line:
                            continue
                        
                        # 跳过表头行
                        if '时间戳' in line and ('小组长姓名' in line or '动作类型' in line):
                            in_table = True
                            continue
                        
                        # 跳过分隔线
                        if line.startswith('|') and '---' in line:
                            continue
                        
                        # 提取数据行
                        if in_table and line.startswith('|') and line.endswith('|'):
                            cells = [cell.strip() for cell in line.split('|')[1:-1]]
                            
                            if len(cells) >= 6:
                                timestamp, leader_name, action_type, description, context, participants = cells[:6]
                                
                                if re.match(r'\d{2}:\d{2}:\d{2}', timestamp):
                                    # 创建唯一标识符用于去重
                                    unique_key = f"{timestamp.strip()}|{leader_name.strip()}|{action_type.strip()}|{description.strip()}"
                                    
                                    if unique_key not in seen_actions:
                                        seen_actions.add(unique_key)
                                        action = {
                                            "timestamp": timestamp.strip(),
                                            "leader_name": leader_name.strip(),
                                            "action_type": action_type.strip(),
                                            "action_category": action_type.strip(),
                                            "description": description.strip(),
                                            "participants": participants.strip(),
                                            "context": context.strip(),
                                            "segment": segment_name
                                        }
                                        actions.append(action)
        
        return actions
    
    def extract_moments_from_segment_content(self, content: str, segment_name: str) -> List[Dict]:
        """从分段内容中提取关键时间节点"""
        moments = []
        seen_moments = set()  # 用于去重
        
        # 查找关键时间节点记录表
        table_patterns = [
            r"## 二、关键时间节点记录表\s*\n(.*?)(?=## 三、|$)",
            r"(\|[^|]*时间戳[^|]*\|[^|]*事件类型[^|]*\|.*?)(?=\n## |\n---|\n\n## |$)"
        ]
        
        for pattern in table_patterns:
            matches = re.findall(pattern, content, re.DOTALL)
            
            for table_content in matches:
                # 使用正则表达式提取表格行
                line_pattern = r'(\d{2}:\d{2}:\d{2})\s*\|\s*([^|]+)\s*\|\s*([^|]+)\s*\|\s*([^|]+)\s*\|\s*([^|]*)'
                line_matches = re.findall(line_pattern, table_content)
                
                for match in line_matches:
                    timestamp, event_type, description, participants, duration = match
                    
                    # 创建唯一标识符用于去重
                    unique_key = f"{timestamp.strip()}|{event_type.strip()}|{description.strip()}"
                    
                    if unique_key not in seen_moments:
                        seen_moments.add(unique_key)
                        moment = {
                            "timestamp": timestamp.strip(),
                            "event_type": event_type.strip(),
                            "event_category": event_type.strip(),
                            "description": description.strip(),
                            "participants": participants.strip(),
                            "duration": duration.strip() if duration.strip() else "",
                            "segment": segment_name
                        }
                        moments.append(moment)
                
                # 如果正则匹配失败，尝试标准Markdown表格格式
                if not line_matches:
                    table_lines = table_content.split('\n')
                    in_table = False
                    
                    for line in table_lines:
                        line = line.strip()
                        if not line:
                            continue
                        
                        # 跳过表头行
                        if '时间戳' in line and ('事件类型' in line or '描述' in line):
                            in_table = True
                            continue
                        
                        # 跳过分隔线
                        if line.startswith('|') and '---' in line:
                            continue
                        
                        # 提取数据行
                        if in_table and line.startswith('|') and line.endswith('|'):
                            cells = [cell.strip() for cell in line.split('|')[1:-1]]
                            
                            if len(cells) >= 4:
                                timestamp = cells[0] if len(cells) > 0 else ""
                                event_type = cells[1] if len(cells) > 1 else ""
                                description = cells[2] if len(cells) > 2 else ""
                                participants = cells[3] if len(cells) > 3 else ""
                                duration = cells[4] if len(cells) > 4 else ""
                                
                                if re.match(r'\d{2}:\d{2}:\d{2}', timestamp):
                                    # 创建唯一标识符用于去重
                                    unique_key = f"{timestamp.strip()}|{event_type.strip()}|{description.strip()}"
                                    
                                    if unique_key not in seen_moments:
                                        seen_moments.add(unique_key)
                                        moment = {
                                            "timestamp": timestamp.strip(),
                                            "event_type": event_type.strip(),
                                            "event_category": event_type.strip(),
                                            "description": description.strip(),
                                            "participants": participants.strip(),
                                            "duration": duration.strip() if duration.strip() else "",
                                            "segment": segment_name
                                        }
                                        moments.append(moment)
        
        return moments
    
    def get_time_range(self, data_list: List[Dict]) -> str:
        """获取数据的时间范围"""
        if not data_list:
            return "无数据"
        
        timestamps = [item.get('timestamp', '') for item in data_list if item.get('timestamp')]
        if not timestamps:
            return "无时间戳"
        
        timestamps.sort()
        return f"{timestamps[0]} - {timestamps[-1]}"
    
    def analyze_chat_statistics(self, chat_content: str, leader_name: str) -> Dict[str, Any]:
        """分析聊天记录的统计数据"""
        print("正在分析聊天记录统计数据...")
        
        # 解析聊天记录
        lines = chat_content.split('\n')
        
        # 统计变量
        total_messages = 0
        leader_messages = 0
        active_users = set()
        user_message_counts = {}
        time_distribution = {'上午': 0, '下午': 0, '晚上': 0}
        
        # 时间格式正则表达式
        time_pattern = r'\*\*(\d{2}:\d{2}:\d{2})\s+([^*]+)\*\*:\s*(.*)'
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # 尝试匹配时间格式
            match = re.match(time_pattern, line)
            if match:
                time_str = match.group(1)
                username = match.group(2).strip()
                message = match.group(3).strip()
                
                # 统计总消息数
                total_messages += 1
                
                # 统计活跃用户
                active_users.add(username)
                
                # 统计每个用户的发言次数
                user_message_counts[username] = user_message_counts.get(username, 0) + 1
                
                # 统计小组长发言次数
                if leader_name in username or username in leader_name:
                    leader_messages += 1
                
                # 统计时间分布
                try:
                    hour = int(time_str.split(':')[0])
                    if hour < 12:
                        time_distribution['上午'] += 1
                    elif hour < 18:
                        time_distribution['下午'] += 1
                    else:
                        time_distribution['晚上'] += 1
                except ValueError:
                    pass
        
        # 计算活跃度指标
        active_user_count = len(active_users)
        
        # 找出最活跃的用户（除了小组长）
        other_users = {k: v for k, v in user_message_counts.items() 
                      if leader_name not in k and k not in leader_name}
        most_active_user = max(other_users.items(), key=lambda x: x[1]) if other_users else ("无", 0)
        
        # 计算小组长参与度
        leader_participation_rate = (leader_messages / total_messages * 100) if total_messages > 0 else 0
        
        # 计算平均每人发言次数
        avg_messages_per_user = total_messages / active_user_count if active_user_count > 0 else 0
        
        # 找出最活跃的时间段
        most_active_period = max(time_distribution.items(), key=lambda x: x[1])[0] if time_distribution else "无数据"
        
        statistics = {
            'total_messages': total_messages,
            'leader_messages': leader_messages,
            'active_user_count': active_user_count,
            'leader_participation_rate': round(leader_participation_rate, 2),
            'avg_messages_per_user': round(avg_messages_per_user, 2),
            'most_active_user': most_active_user[0],
            'most_active_user_count': most_active_user[1],
            'most_active_period': most_active_period,
            'time_distribution': time_distribution,
            'user_message_counts': user_message_counts,
            'active_users_list': list(active_users)
        }
        
        print(f"统计完成: 总消息 {total_messages} 条, 小组长发言 {leader_messages} 次, 活跃用户 {active_user_count} 人")
        
        return statistics
    
    def analyze_chat(self, markdown_file: str, leader_name: str) -> str:
        """分析聊天记录 - 强制分段处理所有文件"""
        print(f"正在读取文件: {markdown_file}")
        chat_content = self.read_markdown_file(markdown_file)
        
        # 检查文件大小
        content_length = len(chat_content)
        print(f"文件大小: {content_length} 字符")
        
        # 强制所有文件进入分段处理模式
        print("启用分段处理模式...")
        
        # 分割聊天记录
        segments = self.split_chat_by_time(chat_content)
        
        # 确保至少有一个段落进行处理
        if len(segments) == 0:
            print("没有找到有效的时间段，创建默认段落...")
            segments = [{
                "name": "全天时段",
                "time_range": "00:00:00 - 23:59:59",
                "content": chat_content,
                "message_count": len([line for line in chat_content.split('\n') if ':' in line])
            }]
        
        print(f"将处理 {len(segments)} 个时间段")
        
        # 分段分析
        segment_results = []
        for i, segment in enumerate(segments, 1):
            try:
                print(f"正在分析第 {i}/{len(segments)} 段: {segment['name']}")
                result = self.analyze_chat_segment(segment, leader_name, i)
                segment_results.append(result)
            except Exception as e:
                print(f"分析第 {i} 段时出错: {e}")
                # 为失败的段落创建默认结果
                error_result = self.create_default_segment_result(segment, leader_name, str(e))
                segment_results.append(error_result)
        
        # 合并结果
        return self.merge_analysis_results(segment_results, segments, leader_name, markdown_file)
    
    def extract_json_from_result(self, result: str) -> Dict[str, Any]:
        """从分析结果中提取JSON数据"""
        # 首先清理结果，移除可能的markdown标记
        cleaned_result = result.strip()
        
        # 移除可能的```json和```标记
        if cleaned_result.startswith('```json'):
            cleaned_result = cleaned_result[7:]  # 移除```json
        if cleaned_result.startswith('```'):
            cleaned_result = cleaned_result[3:]   # 移除```
        if cleaned_result.endswith('```'):
            cleaned_result = cleaned_result[:-3]  # 移除结尾的```
        
        cleaned_result = cleaned_result.strip()
        
        try:
            # 尝试直接解析清理后的结果
            parsed_data = json.loads(cleaned_result)
            print(f"成功解析JSON数据: {len(parsed_data.get('leader_actions', []))} 个动作, {len(parsed_data.get('key_moments', []))} 个时间节点")
            return parsed_data
        except json.JSONDecodeError as e:
            print(f"JSON解析失败: {e}")
            print("尝试修复不完整的JSON数据...")
            
            # 尝试提取JSON部分
            json_start = cleaned_result.find('{')
            if json_start == -1:
                print("警告: 未找到JSON开始标记")
                return {"leader_actions": [], "key_moments": [], "raw_text": result}
            
            # 尝试多种方式找到JSON结束位置
            json_candidates = []
            
            # 方法1: 找最后一个}
            json_end = cleaned_result.rfind('}') + 1
            if json_end > json_start:
                json_candidates.append(cleaned_result[json_start:json_end])
            
            # 方法2: 尝试修复截断的JSON
            json_partial = cleaned_result[json_start:]
            
            # 检查是否有leader_actions数组
            if '"leader_actions"' in json_partial:
                # 尝试提取leader_actions数据
                actions_data = self.extract_partial_data(json_partial, 'leader_actions')
                moments_data = self.extract_partial_data(json_partial, 'key_moments')
                
                if actions_data or moments_data:
                    print(f"部分提取成功: {len(actions_data)} 个动作, {len(moments_data)} 个时间节点")
                    return {
                        "leader_actions": actions_data,
                        "key_moments": moments_data,
                        "source": "partial_extraction"
                    }
            
            # 尝试解析候选JSON
            for i, candidate in enumerate(json_candidates):
                try:
                    parsed_data = json.loads(candidate)
                    print(f"候选JSON {i+1} 解析成功")
                    return parsed_data
                except json.JSONDecodeError:
                    continue
            
            # 如果所有方法都失败，返回空结构
            print("警告: 无法解析JSON格式，将返回原始文本")
            print(f"原始结果前100字符: {result[:100]}...")
            return {"leader_actions": [], "key_moments": [], "raw_text": result}
    
    def extract_partial_data(self, text: str, key: str) -> List[Dict]:
        """从部分文本中提取数组数据"""
        try:
            # 查找数组开始位置
            key_pattern = f'"{key}"\\s*:\\s*\\['
            match = re.search(key_pattern, text)
            if not match:
                return []
            
            start_pos = match.end() - 1  # 包含[
            
            # 查找数组结束位置，考虑嵌套
            bracket_count = 0
            pos = start_pos
            array_end = -1
            
            while pos < len(text):
                char = text[pos]
                if char == '[':
                    bracket_count += 1
                elif char == ']':
                    bracket_count -= 1
                    if bracket_count == 0:
                        array_end = pos + 1
                        break
                pos += 1
            
            if array_end > start_pos:
                array_str = text[start_pos:array_end]
                try:
                    return json.loads(array_str)
                except json.JSONDecodeError:
                    # 尝试修复常见的JSON错误
                    array_str = self.fix_common_json_errors(array_str)
                    try:
                        return json.loads(array_str)
                    except json.JSONDecodeError:
                        pass
            
            return []
        except Exception as e:
            print(f"提取{key}数据时出错: {e}")
            return []
    
    def fix_common_json_errors(self, json_str: str) -> str:
        """修复常见的JSON错误"""
        # 移除末尾的逗号
        json_str = re.sub(r',\s*([}\]])', r'\1', json_str)
        
        # 确保字符串被正确引用
        json_str = re.sub(r'([{,]\s*)([a-zA-Z_][a-zA-Z0-9_]*)\s*:', r'\1"\2":', json_str)
        
        # 修复截断的字符串
        if json_str.count('"') % 2 != 0:
            json_str += '"'
        
        # 修复未闭合的数组或对象
        open_brackets = json_str.count('[') - json_str.count(']')
        open_braces = json_str.count('{') - json_str.count('}')
        
        json_str += ']' * open_brackets
        json_str += '}' * open_braces
        
        return json_str
    
    def extract_data_from_markdown(self, markdown_content: str, leader_name: str) -> Dict[str, Any]:
        """从Markdown内容中提取结构化数据"""
        print("正在从Markdown内容中提取结构化数据...")
        
        leader_actions = []
        key_moments = []
        
        # 提取小组长关键动作
        actions_data = self.extract_leader_actions_from_md(markdown_content)
        leader_actions.extend(actions_data)
        
        # 提取关键时间节点
        moments_data = self.extract_key_moments_from_md(markdown_content)
        key_moments.extend(moments_data)
        
        # 提取统计信息
        stats = self.extract_statistics_from_md(markdown_content)
        
        result = {
            "leader_actions": leader_actions,
            "key_moments": key_moments,
            "statistics": stats,
            "leader_name": leader_name
        }
        
        print(f"提取完成: {len(leader_actions)} 个关键动作, {len(key_moments)} 个关键时间节点")
        return result
    
    def extract_leader_actions_from_md(self, content: str) -> List[Dict]:
        """从Markdown中提取小组长关键动作"""
        actions = []
        
        # 首先按时间段分割内容，查找每个时间段的表格
        segments = self.identify_time_segments_in_content(content)
        
        for segment_name, segment_content in segments.items():
            print(f"处理 {segment_name} 的小组长动作表格...")
            
            # 查找该时间段的小组长关键动作记录表
            table_patterns = [
                r"## 一、小组长关键动作记录表\s*\n.*?(?=## 二、|## [^一]|$)",
                r"(\|[^|]*时间戳[^|]*\|[^|]*小组长姓名[^|]*\|.*?)(?=\n## |\n---|\n\n## |$)"
            ]
            
            table_found = False
            for pattern in table_patterns:
                matches = re.findall(pattern, segment_content, re.DOTALL)
                
                for table_content in matches:
                    table_found = True
                    
                    # 提取表格行数据
                    line_pattern = r'(\d{2}:\d{2}:\d{2})\s*\|\s*([^|]+)\s*\|\s*([^|]+)\s*\|\s*([^|]+)\s*\|\s*([^|]+)\s*\|\s*([^|]+)'
                    line_matches = re.findall(line_pattern, table_content)
                    
                    for match in line_matches:
                        timestamp, leader_name, action_type, description, context, participants = match
                        
                        action = {
                            "timestamp": timestamp.strip(),
                            "leader_name": leader_name.strip(),
                            "action_type": action_type.strip(),
                            "action_category": action_type.strip(),
                            "description": description.strip(),
                            "participants": participants.strip(),
                            "context": context.strip(),
                            "segment": segment_name  # 添加时间段标识
                        }
                        actions.append(action)
                    
                    # 如果正则匹配失败，尝试标准Markdown表格格式
                    if not line_matches:
                        table_lines = table_content.split('\n')
                        in_table = False
                        
                        for line in table_lines:
                            line = line.strip()
                            if not line:
                                continue
                            
                            # 跳过表头行
                            if '时间戳' in line and ('小组长姓名' in line or '动作类型' in line):
                                in_table = True
                                continue
                            
                            # 跳过分隔线
                            if line.startswith('|') and '---' in line:
                                continue
                            
                            # 提取数据行
                            if in_table and line.startswith('|') and line.endswith('|'):
                                cells = [cell.strip() for cell in line.split('|')[1:-1]]
                                
                                if len(cells) >= 6:
                                    timestamp, leader_name, action_type, description, context, participants = cells[:6]
                                    
                                    if re.match(r'\d{2}:\d{2}:\d{2}', timestamp):
                                        action = {
                                            "timestamp": timestamp.strip(),
                                            "leader_name": leader_name.strip(),
                                            "action_type": action_type.strip(),
                                            "action_category": action_type.strip(),
                                            "description": description.strip(),
                                            "participants": participants.strip(),
                                            "context": context.strip(),
                                            "segment": segment_name  # 添加时间段标识
                                        }
                                        actions.append(action)
            
            if table_found:
                segment_actions = [a for a in actions if a.get('segment') == segment_name]
                print(f"从 {segment_name} 提取到 {len(segment_actions)} 个动作")
        
        print(f"从所有时间段提取到 {len(actions)} 个动作")
        
        # 如果没有找到分段表格，尝试旧的格式
        if not actions:
            print("尝试旧格式提取")
            actions = self.extract_leader_actions_from_md_old_format(content)
        
        # 按时间排序
        actions.sort(key=lambda x: x.get('timestamp', '00:00:00'))
        
        return actions
    
    def identify_time_segments_in_content(self, content: str) -> Dict[str, str]:
        """识别内容中的时间段"""
        segments = {}
        
        # 查找时间段标题
        segment_patterns = [
            (r"## (上午时段[^#]*?)(?=## |$)", "上午时段"),
            (r"## (下午时段[^#]*?)(?=## |$)", "下午时段"), 
            (r"## (晚上时段[^#]*?)(?=## |$)", "晚上时段"),
            (r"## ([^#]*上午[^#]*?)(?=## |$)", "上午时段"),
            (r"## ([^#]*下午[^#]*?)(?=## |$)", "下午时段"),
            (r"## ([^#]*晚上[^#]*?)(?=## |$)", "晚上时段")
        ]
        
        for pattern, segment_name in segment_patterns:
            matches = re.findall(pattern, content, re.DOTALL)
            for match in matches:
                if segment_name not in segments:
                    segments[segment_name] = match
        
        # 如果没有找到明确的时间段，将整个内容作为一个段落
        if not segments:
            segments["全天"] = content
        
        return segments
    
    def extract_leader_actions_from_md_old_format(self, content: str) -> List[Dict]:
        """从Markdown中提取小组长关键动作（旧格式）"""
        actions = []
        
        # 定义动作类型映射
        action_types = {
            "任务分配和安排": "task_assignment",
            "会议或活动组织": "meeting_schedule", 
            "重要决策制定": "decision_making",
            "问题解决和指导": "guidance",
            "团队协调和管理": "coordination",
            "项目进度跟踪": "progress_tracking",
            "资源分配": "resource_allocation",
            "重要通知和公告": "announcement"
        }
        
        for action_name, action_type in action_types.items():
            # 查找对应的章节
            pattern = f"### \\d+\\. {re.escape(action_name)}\\s*\\n(.*?)(?=###|##|$)"
            match = re.search(pattern, content, re.DOTALL)
            
            if match:
                section_content = match.group(1).strip()
                
                # 如果不是"本时段无此类动作"，则提取具体动作
                if "本时段无此类动作" not in section_content and section_content:
                    # 提取时间、动作、人员、上下文
                    time_matches = re.findall(r'\*\*时间\*\*:\s*(\d{2}:\d{2}:\d{2})', section_content)
                    action_matches = re.findall(r'\*\*具体动作\*\*:\s*([^\n]+)', section_content)
                    people_matches = re.findall(r'\*\*涉及人员\*\*:\s*([^\n]+)', section_content)
                    context_matches = re.findall(r'\*\*上下文\*\*:\s*([^\n]+)', section_content)
                    
                    # 组合提取的信息
                    for i in range(len(time_matches)):
                        action = {
                            "timestamp": time_matches[i] if i < len(time_matches) else "",
                            "action_type": action_type,
                            "action_category": action_name,
                            "description": action_matches[i] if i < len(action_matches) else "",
                            "participants": people_matches[i] if i < len(people_matches) else "",
                            "context": context_matches[i] if i < len(context_matches) else ""
                        }
                        actions.append(action)
        
        return actions
    
    def extract_key_moments_from_md(self, content: str) -> List[Dict]:
        """从Markdown中提取关键时间节点"""
        moments = []
        
        # 首先按时间段分割内容，查找每个时间段的表格
        segments = self.identify_time_segments_in_content(content)
        
        for segment_name, segment_content in segments.items():
            print(f"处理 {segment_name} 的关键时间节点表格...")
            
            # 查找该时间段的关键时间节点记录表
            table_patterns = [
                r"## 二、关键时间节点记录表\s*\n.*?(?=## 三、|## [^二]|$)",
                r"(\|[^|]*时间戳[^|]*\|[^|]*事件类型[^|]*\|.*?)(?=\n## |\n---|\n\n## |$)"
            ]
            
            table_found = False
            for pattern in table_patterns:
                matches = re.findall(pattern, segment_content, re.DOTALL)
                
                for table_content in matches:
                    table_found = True
                    
                    # 提取表格行数据，格式：时间戳 | 事件类型 | 描述 | 参与者 | 持续时间(分钟)
                    line_pattern = r'(\d{2}:\d{2}:\d{2})\s*\|\s*([^|]+)\s*\|\s*([^|]+)\s*\|\s*([^|]+)\s*\|\s*([^|]*)'
                    line_matches = re.findall(line_pattern, table_content)
                    
                    for match in line_matches:
                        timestamp, event_type, description, participants, duration = match
                        
                        moment = {
                            "timestamp": timestamp.strip(),
                            "event_type": event_type.strip(),
                            "event_category": event_type.strip(),
                            "description": description.strip(),
                            "participants": participants.strip(),
                            "duration": duration.strip() if duration.strip() else "",
                            "segment": segment_name  # 添加时间段标识
                        }
                        moments.append(moment)
                    
                    # 如果正则匹配失败，尝试标准Markdown表格格式
                    if not line_matches:
                        table_lines = table_content.split('\n')
                        in_table = False
                        
                        for line in table_lines:
                            line = line.strip()
                            if not line:
                                continue
                            
                            # 跳过表头行
                            if '时间戳' in line and ('事件类型' in line or '描述' in line):
                                in_table = True
                                continue
                            
                            # 跳过分隔线
                            if line.startswith('|') and '---' in line:
                                continue
                            
                            # 提取数据行
                            if in_table and line.startswith('|') and line.endswith('|'):
                                cells = [cell.strip() for cell in line.split('|')[1:-1]]
                                
                                if len(cells) >= 4:
                                    timestamp = cells[0] if len(cells) > 0 else ""
                                    event_type = cells[1] if len(cells) > 1 else ""
                                    description = cells[2] if len(cells) > 2 else ""
                                    participants = cells[3] if len(cells) > 3 else ""
                                    duration = cells[4] if len(cells) > 4 else ""
                                    
                                    if re.match(r'\d{2}:\d{2}:\d{2}', timestamp):
                                        moment = {
                                            "timestamp": timestamp.strip(),
                                            "event_type": event_type.strip(),
                                            "event_category": event_type.strip(),
                                            "description": description.strip(),
                                            "participants": participants.strip(),
                                            "duration": duration.strip() if duration.strip() else "",
                                            "segment": segment_name  # 添加时间段标识
                                        }
                                        moments.append(moment)
            
            if table_found:
                segment_moments = [m for m in moments if m.get('segment') == segment_name]
                print(f"从 {segment_name} 提取到 {len(segment_moments)} 个时间节点")
        
        print(f"从所有时间段提取到 {len(moments)} 个时间节点")
        
        # 如果没有找到分段表格，尝试旧的格式
        if not moments:
            print("尝试旧格式提取")
            moments = self.extract_key_moments_from_md_old_format(content)
        
        # 按时间排序
        moments.sort(key=lambda x: x.get('timestamp', '00:00:00'))
        
        return moments
    
    def extract_key_moments_from_md_old_format(self, content: str) -> List[Dict]:
        """从Markdown中提取关键时间节点（旧格式）"""
        moments = []
        
        # 定义事件类型映射
        event_types = {
            "活动开始和结束": "activity_start_end",
            "重要讨论高峰期": "peak_discussion",
            "学员积极发言时段": "active_participation", 
            "重要公告或通知": "important_announcement",
            "问题解决关键时刻": "problem_resolution",
            "决策达成时间点": "decision_point",
            "项目里程碑": "milestone",
            "话题转换关键时刻": "topic_change",
            "情绪高涨时刻": "emotional_peak"
        }
        
        for event_name, event_type in event_types.items():
            # 查找对应的章节
            pattern = f"### \\d+\\. {re.escape(event_name)}\\s*\\n(.*?)(?=###|##|$)"
            match = re.search(pattern, content, re.DOTALL)
            
            if match:
                section_content = match.group(1).strip()
                
                # 如果不是"本时段无此类节点"，则提取具体事件
                if "本时段无此类节点" not in section_content and section_content:
                    # 提取时间、事件描述、参与人员、持续时间
                    time_matches = re.findall(r'\*\*时间\*\*:\s*(\d{2}:\d{2}:\d{2})', section_content)
                    desc_matches = re.findall(r'\*\*事件描述\*\*:\s*([^\n]+)', section_content)
                    people_matches = re.findall(r'\*\*参与人员\*\*:\s*([^\n]+)', section_content)
                    duration_matches = re.findall(r'\*\*持续时间\*\*:\s*([^\n]+)', section_content)
                    
                    # 组合提取的信息
                    for i in range(len(time_matches)):
                        moment = {
                            "timestamp": time_matches[i] if i < len(time_matches) else "",
                            "event_type": event_type,
                            "event_category": event_name,
                            "description": desc_matches[i] if i < len(desc_matches) else "",
                            "participants": people_matches[i] if i < len(people_matches) else "",
                            "duration": duration_matches[i] if i < len(duration_matches) else ""
                        }
                        moments.append(moment)
        
        return moments
    
    def extract_statistics_from_md(self, content: str) -> Dict[str, str]:
        """从Markdown中提取统计信息"""
        stats = {}
        
        # 查找统计摘要部分
        pattern = r"## 三、统计摘要\s*\n(.*?)(?=##|$)"
        match = re.search(pattern, content, re.DOTALL)
        
        if match:
            stats_content = match.group(1).strip()
            
            # 提取各项统计信息
            stats_patterns = {
                "analysis_period": r"\*\*分析时间段\*\*:\s*([^\n]+)",
                "total_messages": r"\*\*小组长总发言次数\*\*:\s*([^\n]+)",
                "total_actions": r"\*\*关键动作总数\*\*:\s*([^\n]+)",
                "total_moments": r"\*\*关键时间节点总数\*\*:\s*([^\n]+)",
                "active_period": r"\*\*最活跃时间段\*\*:\s*([^\n]+)",
                "main_activities": r"\*\*主要活动类型\*\*:\s*([^\n]+)"
            }
            
            for key, pattern in stats_patterns.items():
                match = re.search(pattern, stats_content)
                if match:
                    stats[key] = match.group(1).strip()
        
        return stats
    
    def create_leader_actions_table(self, actions: List[Dict]) -> str:
        """创建小组长动作表格"""
        if not actions:
            return "## 小组长关键动作分析\n\n暂无数据\n\n"
        
        # 创建DataFrame并重命名列为中文
        df_actions = pd.DataFrame(actions)
        
        # 定义列名映射（英文到中文）
        column_mapping = {
            'timestamp': '时间',
            'action_type': '动作类型编码',
            'action_category': '动作类型',
            'description': '具体动作描述',
            'participants': '涉及人员',
            'context': '上下文背景',
            'segment': '时间段'
        }
        
        # 重命名列
        df_actions = df_actions.rename(columns=column_mapping)
        
        # 处理participants列表转换为字符串
        if '涉及人员' in df_actions.columns:
            df_actions['涉及人员'] = df_actions['涉及人员'].apply(
                lambda x: ', '.join(x) if isinstance(x, list) else str(x)
            )
        
        # 选择要显示的列（去掉编码列，只保留中文列）
        display_columns = ['时间', '动作类型', '具体动作描述', '涉及人员', '上下文背景']
        if '时间段' in df_actions.columns:
            display_columns.append('时间段')
        
        # 只保留存在的列
        available_columns = [col for col in display_columns if col in df_actions.columns]
        df_display = df_actions[available_columns]
        
        # 生成Markdown表格
        table_md = "## 小组长关键动作分析\n\n"
        table_md += df_display.to_markdown(index=False, tablefmt='pipe')
        table_md += "\n\n"
        
        return table_md
    
    def create_key_moments_table(self, moments: List[Dict]) -> str:
        """创建关键时间节点表格"""
        if not moments:
            return "## 关键时间节点分析\n\n暂无数据\n\n"
        
        # 创建DataFrame并重命名列为中文
        df_moments = pd.DataFrame(moments)
        
        # 定义列名映射（英文到中文）
        column_mapping = {
            'timestamp': '时间',
            'event_type': '事件类型编码',
            'event_category': '事件类型',
            'description': '事件描述',
            'participants': '参与人员',
            'duration': '持续时间',
            'segment': '时间段'
        }
        
        # 重命名列
        df_moments = df_moments.rename(columns=column_mapping)
        
        # 处理participants列表转换为字符串
        if '参与人员' in df_moments.columns:
            df_moments['参与人员'] = df_moments['参与人员'].apply(
                lambda x: ', '.join(x) if isinstance(x, list) else str(x)
            )
        
        # 选择要显示的列（去掉编码列，只保留中文列）
        display_columns = ['时间', '事件类型', '事件描述', '参与人员', '持续时间']
        if '时间段' in df_moments.columns:
            display_columns.append('时间段')
        
        # 只保留存在的列
        available_columns = [col for col in display_columns if col in df_moments.columns]
        df_display = df_moments[available_columns]
        
        # 生成Markdown表格
        table_md = "## 关键时间节点分析\n\n"
        table_md += df_display.to_markdown(index=False, tablefmt='pipe')
        table_md += "\n\n"
        
        return table_md
    
    def create_summary_statistics(self, data: Dict[str, Any], leader_name: str) -> str:
        """创建统计摘要"""
        actions = data.get('leader_actions', [])
        moments = data.get('key_moments', [])
        stats = data.get('statistics', {})
        
        summary = f"# {leader_name} 群聊分析表格报告\n\n"
        summary += "## 统计摘要\n\n"
        summary += f"- **小组长姓名**: {leader_name}\n"
        summary += f"- **关键动作总数**: {len(actions)}\n"
        summary += f"- **关键时间节点总数**: {len(moments)}\n"
        
        # 添加从原始统计中提取的信息
        if stats:
            if stats.get('analysis_period'):
                summary += f"- **分析时间段**: {stats['analysis_period']}\n"
            if stats.get('total_messages'):
                summary += f"- **小组长总发言次数**: {stats['total_messages']}\n"
            if stats.get('active_period'):
                summary += f"- **最活跃时间段**: {stats['active_period']}\n"
            if stats.get('main_activities'):
                summary += f"- **主要活动类型**: {stats['main_activities']}\n"
        
        summary += "\n"
        
        # 动作类型统计
        if actions:
            action_types = {}
            for action in actions:
                action_category = action.get('action_category', action.get('action_type', '未知'))
                action_types[action_category] = action_types.get(action_category, 0) + 1
            
            summary += "### 动作类型分布\n\n"
            for action_type, count in sorted(action_types.items()):
                summary += f"- **{action_type}**: {count}次\n"
            summary += "\n"
        
        # 事件类型统计
        if moments:
            event_types = {}
            for moment in moments:
                event_category = moment.get('event_category', moment.get('event_type', '未知'))
                event_types[event_category] = event_types.get(event_category, 0) + 1
            
            summary += "### 事件类型分布\n\n"
            for event_type, count in sorted(event_types.items()):
                summary += f"- **{event_type}**: {count}次\n"
            summary += "\n"
        
        return summary
    
    def save_excel_tables(self, data: Dict[str, Any], leader_name: str, markdown_file: str = None) -> str:
        """保存Excel格式的表格"""
        default_dir = "analysis_results"
        
        # 如果提供了markdown_file，尝试从文件名中提取组名
        if markdown_file:
            file_base_name = os.path.splitext(os.path.basename(markdown_file))[0]
            # 尝试解析组名，假设格式为：数字+组名_小组长 或 数字+组名·核浪+小组长
            import re
            
            # 匹配模式1: 数字+组名_小组长 (如: 13鲜潮将至_核浪)
            match1 = re.match(r'(\d+)([^_]+)_(.+)', file_base_name)
            # 匹配模式2: 数字+组名·核浪+小组长 (如: 13鲜潮将至·核浪)
            match2 = re.match(r'(\d+)([^·]+)·核浪(.+)?', file_base_name)
            
            if match1:
                group_number = match1.group(1)
                group_name = match1.group(2)
                file_leader = match1.group(3)
                clean_name = self.sanitize_filename(f"{group_name}_{leader_name}")
            elif match2:
                group_number = match2.group(1)
                group_name = match2.group(2)
                clean_name = self.sanitize_filename(f"{group_name}_{leader_name}")
            else:
                # 如果无法解析，使用文件基础名称
                clean_name = self.sanitize_filename(f"{file_base_name}_{leader_name}")
        else:
            # 如果没有提供markdown_file，使用原来的命名方式
            clean_name = self.sanitize_filename(leader_name)
        
        excel_file = os.path.join(default_dir, f"analysis_tables_{clean_name}.xlsx")
        
        try:
            with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
                # 保存小组长关键动作记录表
                if data.get('leader_actions'):
                    actions_list = data['leader_actions']
                    print(f"正在处理 {len(actions_list)} 个小组长动作...")
                    
                    # 创建标准化的数据结构
                    actions_data = []
                    for action in actions_list:
                        action_row = {
                            '时间': action.get('timestamp', ''),
                            '小组长姓名': action.get('leader_name', leader_name),
                            '动作类型': action.get('action_type', action.get('action_category', '')),
                            '具体动作描述': action.get('description', ''),
                            '上下文背景': action.get('context', ''),
                            '涉及人员': action.get('participants', '')
                        }
                        
                        # 如果有时间段信息，添加到表格中
                        if action.get('segment'):
                            action_row['时间段'] = action.get('segment')
                        
                        actions_data.append(action_row)
                    
                    if actions_data:
                        df_actions = pd.DataFrame(actions_data)
                        
                        # 处理涉及人员列表转换为字符串
                        if '涉及人员' in df_actions.columns:
                            df_actions['涉及人员'] = df_actions['涉及人员'].apply(
                                lambda x: ', '.join(x) if isinstance(x, list) else str(x)
                            )
                        
                        # 按时间排序
                        df_actions = df_actions.sort_values('时间', na_position='last')
                        
                        df_actions.to_excel(writer, sheet_name='小组长关键动作记录表', index=False)
                        print(f"小组长动作表已保存，共 {len(df_actions)} 条记录")
                
                # 保存关键时间节点记录表
                if data.get('key_moments'):
                    moments_list = data['key_moments']
                    print(f"正在处理 {len(moments_list)} 个关键时间节点...")
                    
                    # 创建标准化的数据结构
                    moments_data = []
                    for moment in moments_list:
                        moment_row = {
                            '时间': moment.get('timestamp', ''),
                            '事件类型': moment.get('event_type', moment.get('event_category', '')),
                            '事件描述': moment.get('description', ''),
                            '参与人员': moment.get('participants', ''),
                            '持续时间(分钟)': moment.get('duration', '')
                        }
                        
                        # 如果有时间段信息，添加到表格中
                        if moment.get('segment'):
                            moment_row['时间段'] = moment.get('segment')
                        
                        moments_data.append(moment_row)
                    
                    if moments_data:
                        df_moments = pd.DataFrame(moments_data)
                        
                        # 处理参与人员列表转换为字符串
                        if '参与人员' in df_moments.columns:
                            df_moments['参与人员'] = df_moments['参与人员'].apply(
                                lambda x: ', '.join(x) if isinstance(x, list) else str(x)
                            )
                        
                        # 按时间排序
                        df_moments = df_moments.sort_values('时间', na_position='last')
                        
                        df_moments.to_excel(writer, sheet_name='关键时间节点记录表', index=False)
                        print(f"关键时间节点表已保存，共 {len(df_moments)} 条记录")
                
                # 创建详细统计摘要表
                stats_data = []
                
                # 基本统计信息
                basic_stats = [
                    {'统计项目': '小组长姓名', '统计结果': leader_name},
                    {'统计项目': '实际关键动作总数', '统计结果': len(data.get('leader_actions', []))},
                    {'统计项目': '实际关键时间节点总数', '统计结果': len(data.get('key_moments', []))},
                ]
                
                # 如果有原始统计信息，添加进来
                if data.get('statistics'):
                    stats = data['statistics']
                    stats_mapping = {
                        'analysis_period': '分析时间段',
                        'segments_processed': '处理的时间段数',
                        'total_actions': '提取的动作总数',
                        'total_moments': '提取的时间节点总数'
                    }
                    
                    for key, chinese_name in stats_mapping.items():
                        if key in stats:
                            basic_stats.append({
                                '统计项目': chinese_name,
                                '统计结果': stats[key]
                            })
                
                stats_data.extend(basic_stats)
                
                # 如果有聊天统计数据，添加进来
                if hasattr(self, 'chat_statistics') and self.chat_statistics:
                    chat_stats = self.chat_statistics
                    
                    chat_stats_data = [
                        {'统计项目': '群聊总聊天数', '统计结果': f"{chat_stats.get('total_messages', 0)} 条"},
                        {'统计项目': '小组长发言次数', '统计结果': f"{chat_stats.get('leader_messages', 0)} 次"},
                        {'统计项目': '群聊活跃人数', '统计结果': f"{chat_stats.get('active_user_count', 0)} 人"},
                        {'统计项目': '小组长参与度', '统计结果': f"{chat_stats.get('leader_participation_rate', 0)}%"},
                        {'统计项目': '平均每人发言次数', '统计结果': f"{chat_stats.get('avg_messages_per_user', 0)} 次"},
                        {'统计项目': '最活跃用户', '统计结果': chat_stats.get('most_active_user', '无')},
                        {'统计项目': '最活跃用户发言次数', '统计结果': f"{chat_stats.get('most_active_user_count', 0)} 次"},
                        {'统计项目': '最活跃时间段', '统计结果': chat_stats.get('most_active_period', '无数据')},
                    ]
                    
                    stats_data.extend(chat_stats_data)
                    
                    # 添加时间分布统计
                    time_dist = chat_stats.get('time_distribution', {})
                    for period, count in time_dist.items():
                        stats_data.append({
                            '统计项目': f'{period}时段消息数',
                            '统计结果': f"{count} 条"
                        })
                    
                    # 添加活跃用户列表（前10名）
                    user_counts = chat_stats.get('user_message_counts', {})
                    sorted_users = sorted(user_counts.items(), key=lambda x: x[1], reverse=True)[:10]
                    
                    stats_data.append({
                        '统计项目': '--- 用户发言排行榜 ---',
                        '统计结果': '--- TOP 10 ---'
                    })
                    
                    for i, (username, count) in enumerate(sorted_users, 1):
                        stats_data.append({
                            '统计项目': f'第{i}名用户',
                            '统计结果': f"{username} ({count}次)"
                        })
                
                if stats_data:
                    df_stats = pd.DataFrame(stats_data)
                    df_stats.to_excel(writer, sheet_name='详细统计摘要', index=False)
                    print(f"详细统计摘要表已保存，共 {len(df_stats)} 项统计")
            
            print(f"Excel表格已保存到: {excel_file}")
            return excel_file
        except Exception as e:
            print(f"保存Excel文件失败: {e}")
            import traceback
            traceback.print_exc()
            return ""
    
    def sanitize_filename(self, filename: str) -> str:
        """清理文件名，移除不合法字符"""
        # 移除或替换Windows文件名中不允许的字符
        illegal_chars = r'[<>:"/\\|?*]'
        filename = re.sub(illegal_chars, '_', filename)
        # 移除emoji和其他特殊Unicode字符，保留中文、英文、数字、空格、连字符、下划线、点号
        filename = re.sub(r'[^\w\s._-]', '_', filename)
        # 移除多余的空格和下划线
        filename = re.sub(r'[_\s]+', '_', filename).strip('_')
        return filename
    
    def get_chat_base_name(self, markdown_file: str) -> str:
        """获取聊天记录的基础名称（用于创建文件夹）"""
        # 从文件路径中提取文件名（不含扩展名）
        base_name = os.path.splitext(os.path.basename(markdown_file))[0]
        return self.sanitize_filename(base_name)
    
    def save_result(self, result: str, leader_name: str, output_file: str = None, markdown_file: str = None):
        """保存分析结果"""
        # 创建默认保存目录
        default_dir = "analysis_results"
        if not os.path.exists(default_dir):
            os.makedirs(default_dir)
        
        if output_file is None:
            # 如果提供了markdown_file，创建以聊天记录命名的文件夹
            if markdown_file:
                chat_base_name = self.get_chat_base_name(markdown_file)
                chat_folder = os.path.join(default_dir, chat_base_name)
                if not os.path.exists(chat_folder):
                    os.makedirs(chat_folder)
                
                # 清理文件名
                clean_leader_name = self.sanitize_filename(leader_name)
                output_file = os.path.join(chat_folder, f"analysis_result_{clean_leader_name}.md")
            else:
                # 兼容旧的保存方式
                clean_leader_name = self.sanitize_filename(leader_name)
                output_file = os.path.join(default_dir, f"analysis_result_{clean_leader_name}.md")
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(result)
            print(f"分析结果已保存到: {output_file}")
        except Exception as e:
            print(f"保存文件失败: {e}")
            # 尝试保存到当前目录
            fallback_file = f"analysis_result_{self.sanitize_filename(leader_name)}.md"
            try:
                with open(fallback_file, 'w', encoding='utf-8') as f:
                    f.write(result)
                print(f"已保存到当前目录: {fallback_file}")
            except Exception as e2:
                print(f"保存到当前目录也失败: {e2}")


def main():
    print("="*50)
    print("群聊记录分析工具")
    print("="*50)
    
    # 通过input获取用户输入
    markdown_file = input("请输入聊天记录的markdown文件路径: ").strip()
    leader_name = input("请输入小组长姓名: ").strip()
    
    # 询问是否需要自定义输出文件路径
    custom_output = input("是否需要自定义输出文件路径？(y/n，默认为n): ").strip().lower()
    output_file = None
    if custom_output in ['y', 'yes']:
        output_file = input("请输入输出文件路径: ").strip()
    
    # 验证输入
    if not markdown_file:
        print("错误: 文件路径不能为空")
        sys.exit(1)
    
    if not leader_name:
        print("错误: 小组长姓名不能为空")
        sys.exit(1)
    
    # 智谱API密钥
    api_key = "e601f05d308a42be8d5f3e40a98ea324.aPTWcglZd4DljsUY"
    
    # 创建分析器实例
    analyzer = ChatAnalyzer(api_key)
    
    # 读取原始聊天记录用于统计分析
    print("正在读取原始聊天记录进行统计分析...")
    original_chat_content = analyzer.read_markdown_file(markdown_file)
    
    # 分析聊天统计数据
    chat_statistics = analyzer.analyze_chat_statistics(original_chat_content, leader_name)
    analyzer.chat_statistics = chat_statistics  # 存储到类属性中
    
    # 执行分析
    result = analyzer.analyze_chat(markdown_file, leader_name)
    
    # 输出分析结果
    print("\n" + "="*50)
    print("分析结果:")
    print("="*50)
    print(result)
    
    # 保存分析结果
    analyzer.save_result(result, leader_name, output_file, markdown_file)
    
    # 从Markdown内容中提取数据并生成表格
    print("\n" + "="*50)
    print("正在从Markdown内容中提取数据并生成表格...")
    print("="*50)
    
    # 优先使用分段文件提取方法
    if hasattr(analyzer, 'segment_files') and analyzer.segment_files:
        print("使用分段文件提取方法...")
        extracted_data = analyzer.extract_data_from_segment_files(leader_name)
    else:
        print("使用默认提取方法...")
        extracted_data = analyzer.extract_data_from_markdown(result, leader_name)
    
    if extracted_data.get('leader_actions') or extracted_data.get('key_moments'):
        # 创建表格报告
        table_report = analyzer.create_summary_statistics(extracted_data, leader_name)
        table_report += analyzer.create_leader_actions_table(extracted_data.get('leader_actions', []))
        table_report += analyzer.create_key_moments_table(extracted_data.get('key_moments', []))
        
        # 保存表格报告
        if output_file is None:
            # 创建以聊天记录命名的文件夹
            chat_base_name = analyzer.get_chat_base_name(markdown_file)
            chat_folder = os.path.join("analysis_results", chat_base_name)
            if not os.path.exists(chat_folder):
                os.makedirs(chat_folder)
            
            clean_leader_name = analyzer.sanitize_filename(leader_name)
            table_output_file = os.path.join(chat_folder, f"table_report_{clean_leader_name}.md")
        else:
            table_output_file = output_file.replace('.md', '_table.md')
        
        try:
            with open(table_output_file, 'w', encoding='utf-8') as f:
                f.write(table_report)
            print(f"表格报告已保存到: {table_output_file}")
        except Exception as e:
            print(f"保存表格报告失败: {e}")
        
        # 保存Excel格式的表格
        analyzer.save_excel_tables(extracted_data, leader_name, markdown_file)
        
        # 显示表格预览
        print("\n" + "="*50)
        print("表格报告预览:")
        print("="*50)
        print(table_report[:1000] + "..." if len(table_report) > 1000 else table_report)
        
    else:
        print("警告: 未能从Markdown内容中提取到有效数据")


if __name__ == "__main__":
    main()
